/**
 * Configuration File - ملف التكوين
 * أداة تحرير خلايا Excel بالذكاء الصناعي
 */

// إعدادات التطبيق الأساسية
const APP_CONFIG = {
    // معلومات التطبيق
    name: 'أداة تحرير خلايا Excel بالذكاء الصناعي',
    version: '1.0.0',
    author: 'AI Tools Developer',
    
    // إعدادات API
    api: {
        // مفاتيح Gemini API
        geminiKeys: [
            'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
            'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
            'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
            'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
            'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc',
            'AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM',
            'AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds',
            'AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk',
            'AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg',
            'AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ',
            'AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4',
            'AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo',
            'AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k',
            'AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA',
            'AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ'
        ],
        
        // رابط API الأساسي
        baseUrl: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
        
        // إعدادات الطلبات
        timeout: 30000, // 30 ثانية
        maxRetries: 3,
        retryDelay: 2000, // 2 ثانية
        
        // حدود API
        maxTokensPerRequest: 8000,
        maxRequestsPerMinute: 60,
        maxRequestsPerDay: 1500
    },
    
    // إعدادات المعالجة
    processing: {
        // حجم الدفعة الافتراضي
        defaultBatchSize: 10,
        
        // أحجام الدفعات المتاحة
        batchSizes: [5, 10, 20, 50],
        
        // التأخير بين الطلبات (بالثواني)
        defaultDelay: 2,
        minDelay: 1,
        maxDelay: 10,
        
        // الحد الأقصى لحجم النص لكل خلية
        maxCellLength: 2000,
        
        // الحد الأقصى لعدد الخلايا في المرة الواحدة
        maxCellsPerSession: 1000
    },
    
    // البرومبت الافتراضي
    defaultPrompt: `قم بتحسين الوصف للسيو وأضف جداول في حالة لم يحتوي الوصف الأصلي على جدول مع قوائم نقطية وأسئلة شائعة. اجعل المحتوى أكثر جاذبية واحترافية مع الحفاظ على المعنى الأصلي.

متطلبات التحسين:
1. تحسين العنوان ليكون جذاب ومتوافق مع السيو
2. إضافة وصف مفصل ومفيد
3. إنشاء قائمة بالمميزات الرئيسية
4. إضافة جدول مقارنة أو مواصفات (إذا كان مناسباً)
5. إضافة قسم أسئلة شائعة
6. استخدام كلمات مفتاحية مناسبة
7. جعل المحتوى سهل القراءة ومنظم

تأكد من أن النتيجة النهائية تحتوي على المحتوى المحسن فقط بدون أي مقدمات أو تعليقات إضافية.`,
    
    // إعدادات الواجهة
    ui: {
        // الألوان
        colors: {
            primary: '#2563eb',
            secondary: '#64748b',
            success: '#059669',
            warning: '#d97706',
            danger: '#dc2626',
            info: '#0891b2'
        },
        
        // الخطوط
        fonts: {
            primary: 'Cairo',
            secondary: 'Tajawal'
        },
        
        // الرسائل
        messages: {
            success: {
                fileUploaded: 'تم رفع الملف بنجاح',
                processingComplete: 'تم إكمال المعالجة بنجاح',
                dataExported: 'تم تصدير البيانات بنجاح',
                settingsSaved: 'تم حفظ الإعدادات بنجاح'
            },
            error: {
                fileUploadFailed: 'فشل في رفع الملف',
                processingFailed: 'فشل في المعالجة',
                apiError: 'خطأ في الاتصال بالخدمة',
                invalidFile: 'نوع الملف غير مدعوم',
                noData: 'لا توجد بيانات للمعالجة',
                exportFailed: 'فشل في تصدير البيانات'
            },
            warning: {
                largeFile: 'الملف كبير الحجم، قد تستغرق المعالجة وقتاً أطول',
                apiLimitReached: 'تم الوصول لحد استخدام API',
                processingPaused: 'تم إيقاف المعالجة مؤقتاً',
                unsavedChanges: 'يوجد تغييرات غير محفوظة'
            },
            info: {
                processingStarted: 'بدأت عملية المعالجة',
                switchingApiKey: 'جاري التبديل لمفتاح API آخر',
                preparingExport: 'جاري تحضير ملف التصدير'
            }
        }
    },
    
    // إعدادات التصدير
    export: {
        // أنواع الملفات المدعومة
        supportedFormats: ['html', 'excel', 'text', 'pdf', 'zip'],
        
        // إعدادات HTML
        html: {
            includeStyles: true,
            includeSearch: true,
            responsive: true
        },
        
        // إعدادات Excel
        excel: {
            sheetName: 'النتائج المحسنة',
            includeOriginal: true,
            autoWidth: true
        },
        
        // إعدادات PDF
        pdf: {
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4',
            margin: 20,
            fontSize: 12,
            fontFamily: 'Arial'
        }
    },
    
    // إعدادات التخزين المحلي
    storage: {
        // مفاتيح التخزين
        keys: {
            settings: 'excel_editor_settings',
            history: 'excel_editor_history',
            apiUsage: 'excel_editor_api_usage',
            lastSession: 'excel_editor_last_session'
        },
        
        // مدة الاحتفاظ بالبيانات (بالأيام)
        retentionDays: 30
    },
    
    // إعدادات الأمان
    security: {
        // تشفير البيانات المحلية
        encryptLocalData: false,
        
        // تنظيف البيانات الحساسة
        clearSensitiveData: true,
        
        // مهلة انتهاء الجلسة (بالدقائق)
        sessionTimeout: 60
    },
    
    // إعدادات الأداء
    performance: {
        // تمكين التخزين المؤقت
        enableCaching: true,
        
        // حجم التخزين المؤقت (بالميجابايت)
        cacheSize: 50,
        
        // تحسين الذاكرة
        memoryOptimization: true,
        
        // تحديث الواجهة بشكل دوري
        uiUpdateInterval: 500 // بالميلي ثانية
    }
};

// إعدادات البيئة
const ENV_CONFIG = {
    // بيئة التطوير
    development: {
        debug: true,
        logging: true,
        apiTimeout: 60000
    },
    
    // بيئة الإنتاج
    production: {
        debug: false,
        logging: false,
        apiTimeout: 30000
    }
};

// تحديد البيئة الحالية
const CURRENT_ENV = 'production';

// دمج إعدادات البيئة مع الإعدادات الأساسية
const CONFIG = {
    ...APP_CONFIG,
    ...ENV_CONFIG[CURRENT_ENV]
};

// تصدير الإعدادات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else {
    window.CONFIG = CONFIG;
}

// دوال مساعدة للإعدادات
const ConfigHelper = {
    // الحصول على قيمة من الإعدادات
    get: function(path, defaultValue = null) {
        const keys = path.split('.');
        let value = CONFIG;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return defaultValue;
            }
        }
        
        return value;
    },
    
    // تحديث قيمة في الإعدادات
    set: function(path, value) {
        const keys = path.split('.');
        let target = CONFIG;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in target) || typeof target[key] !== 'object') {
                target[key] = {};
            }
            target = target[key];
        }
        
        target[keys[keys.length - 1]] = value;
    },
    
    // التحقق من وجود قيمة
    has: function(path) {
        return this.get(path) !== null;
    },
    
    // إعادة تعيين الإعدادات للقيم الافتراضية
    reset: function() {
        Object.assign(CONFIG, APP_CONFIG, ENV_CONFIG[CURRENT_ENV]);
    }
};

// تصدير المساعد
if (typeof module !== 'undefined' && module.exports) {
    module.exports.ConfigHelper = ConfigHelper;
} else {
    window.ConfigHelper = ConfigHelper;
}
