/**
 * UI Manager - مدير واجهة المستخدم
 * إدارة التفاعلات والعرض
 */

class UIManager {
    constructor() {
        this.fileProcessor = new FileProcessor();
        this.aiEngine = window.aiEngine;
        this.exportManager = null;
        this.settingsManager = null;
        
        this.elements = {};
        this.currentData = [];
        this.filteredData = [];
        this.searchQuery = '';
        this.currentFilter = 'all';
        
        this.initializeElements();
        this.bindEvents();
        this.setupAIEngineHandlers();
    }
    
    /**
     * تهيئة عناصر الواجهة
     */
    initializeElements() {
        this.elements = {
            // عناصر الإدخال
            fileUploadArea: document.getElementById('fileUploadArea'),
            excelFile: document.getElementById('excelFile'),
            textInput: document.getElementById('textInput'),
            promptInput: document.getElementById('promptInput'),
            batchSize: document.getElementById('batchSize'),
            delayBetweenRequests: document.getElementById('delayBetweenRequests'),
            
            // أزرار التحكم
            startProcessing: document.getElementById('startProcessing'),
            pauseProcessing: document.getElementById('pauseProcessing'),
            stopProcessing: document.getElementById('stopProcessing'),
            
            // قسم التقدم
            progressSection: document.getElementById('progressSection'),
            processedCount: document.getElementById('processedCount'),
            totalCount: document.getElementById('totalCount'),
            currentApiKey: document.getElementById('currentApiKey'),
            estimatedTime: document.getElementById('estimatedTime'),
            mainProgressBar: document.getElementById('mainProgressBar'),
            progressPercentage: document.getElementById('progressPercentage'),
            currentProcessingText: document.getElementById('currentProcessingText'),
            
            // قسم النتائج
            resultsSection: document.getElementById('resultsSection'),
            searchToggle: document.getElementById('searchToggle'),
            searchBox: document.getElementById('searchBox'),
            searchInput: document.getElementById('searchInput'),
            resultsTable: document.getElementById('resultsTable'),
            resultsTableBody: document.getElementById('resultsTableBody'),
            
            // أزرار التصدير
            exportHtml: document.getElementById('exportHtml'),
            exportExcel: document.getElementById('exportExcel'),
            exportText: document.getElementById('exportText'),
            exportPdf: document.getElementById('exportPdf'),
            exportAll: document.getElementById('exportAll'),
            
            // أزرار أخرى
            settingsBtn: document.getElementById('settingsBtn'),
            helpBtn: document.getElementById('helpBtn')
        };
    }
    
    /**
     * ربط الأحداث
     */
    bindEvents() {
        // أحداث رفع الملفات
        this.elements.fileUploadArea.addEventListener('click', () => {
            this.elements.excelFile.click();
        });
        
        this.elements.fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.elements.fileUploadArea.classList.add('dragover');
        });
        
        this.elements.fileUploadArea.addEventListener('dragleave', () => {
            this.elements.fileUploadArea.classList.remove('dragover');
        });
        
        this.elements.fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.elements.fileUploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileUpload(files[0]);
            }
        });
        
        this.elements.excelFile.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileUpload(e.target.files[0]);
            }
        });
        
        // أحداث النص المُلصق
        this.elements.textInput.addEventListener('input', () => {
            this.handleTextInput();
        });
        
        // أحداث أزرار التحكم
        this.elements.startProcessing.addEventListener('click', () => {
            this.startProcessing();
        });
        
        this.elements.pauseProcessing.addEventListener('click', () => {
            this.pauseProcessing();
        });
        
        this.elements.stopProcessing.addEventListener('click', () => {
            this.stopProcessing();
        });
        
        // أحداث البحث
        this.elements.searchToggle.addEventListener('click', () => {
            this.toggleSearch();
        });
        
        this.elements.searchInput.addEventListener('input', (e) => {
            this.handleSearch(e.target.value);
        });
        
        // أحداث التصدير
        this.elements.exportHtml.addEventListener('click', () => this.exportData('html'));
        this.elements.exportExcel.addEventListener('click', () => this.exportData('excel'));
        this.elements.exportText.addEventListener('click', () => this.exportData('text'));
        this.elements.exportPdf.addEventListener('click', () => this.exportData('pdf'));
        this.elements.exportAll.addEventListener('click', () => this.exportData('all'));
        
        // أحداث أخرى
        this.elements.settingsBtn.addEventListener('click', () => this.showSettings());
        this.elements.helpBtn.addEventListener('click', () => this.showHelp());
    }
    
    /**
     * إعداد معالجات محرك الذكاء الصناعي
     */
    setupAIEngineHandlers() {
        this.aiEngine.initialize(this.fileProcessor);
        
        this.aiEngine.setEventHandlers({
            onProgress: (processed, total, message, stats) => {
                this.updateProgress(processed, total, message, stats);
            },
            onComplete: (stats) => {
                this.onProcessingComplete(stats);
            },
            onError: (error, stats) => {
                this.onProcessingError(error, stats);
            },
            onStatusChange: (status, stats) => {
                this.onStatusChange(status, stats);
            }
        });
    }
    
    /**
     * معالجة رفع الملف
     */
    async handleFileUpload(file) {
        try {
            this.showLoading('جاري قراءة الملف...');
            
            // التحقق من صحة الملف
            const validation = this.fileProcessor.validateFile(file);
            if (!validation.isValid) {
                this.showError(validation.errors.join('<br>'));
                return;
            }
            
            // قراءة الملف
            const result = await this.fileProcessor.readExcelFile(file);
            
            if (result.success) {
                // تحميل البيانات
                const loadResult = this.fileProcessor.loadData(result.data);
                
                this.showSuccess(`تم رفع الملف بنجاح. تم العثور على ${loadResult.totalCells} خلية للمعالجة.`);
                this.updateDataPreview(loadResult.preview);
                this.enableProcessingControls();
                
            } else {
                this.showError(result.error);
            }
            
        } catch (error) {
            this.showError('خطأ في معالجة الملف: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * معالجة النص المُلصق
     */
    handleTextInput() {
        const text = this.elements.textInput.value.trim();
        
        if (text === '') {
            this.disableProcessingControls();
            return;
        }
        
        try {
            const result = this.fileProcessor.processTextInput(text);
            
            if (result.success) {
                const loadResult = this.fileProcessor.loadData(result.data);
                this.updateDataPreview(loadResult.preview);
                this.enableProcessingControls();
            }
            
        } catch (error) {
            this.showError(error.message);
            this.disableProcessingControls();
        }
    }
    
    /**
     * بدء المعالجة
     */
    async startProcessing() {
        try {
            const prompt = this.elements.promptInput.value.trim();
            const batchSize = parseInt(this.elements.batchSize.value);
            const delay = parseInt(this.elements.delayBetweenRequests.value);
            
            if (!prompt) {
                this.showError('يرجى إدخال التعليمات للذكاء الصناعي');
                return;
            }
            
            // تحديث واجهة المستخدم
            this.elements.startProcessing.disabled = true;
            this.elements.pauseProcessing.disabled = false;
            this.elements.stopProcessing.disabled = false;
            this.elements.progressSection.style.display = 'block';
            
            // بدء المعالجة
            await this.aiEngine.startProcessing(prompt, batchSize, delay);
            
        } catch (error) {
            this.showError('خطأ في بدء المعالجة: ' + error.message);
            this.resetProcessingControls();
        }
    }
    
    /**
     * إيقاف المعالجة مؤقتاً
     */
    pauseProcessing() {
        if (this.aiEngine.pauseProcessing()) {
            this.elements.pauseProcessing.textContent = 'استئناف';
            this.elements.pauseProcessing.onclick = () => this.resumeProcessing();
            this.showInfo('تم إيقاف المعالجة مؤقتاً');
        }
    }
    
    /**
     * استئناف المعالجة
     */
    resumeProcessing() {
        if (this.aiEngine.resumeProcessing()) {
            this.elements.pauseProcessing.textContent = 'إيقاف مؤقت';
            this.elements.pauseProcessing.onclick = () => this.pauseProcessing();
            this.showInfo('تم استئناف المعالجة');
        }
    }
    
    /**
     * إيقاف المعالجة نهائياً
     */
    stopProcessing() {
        if (this.aiEngine.stopProcessing()) {
            this.resetProcessingControls();
            this.showWarning('تم إيقاف المعالجة');
        }
    }
    
    /**
     * تحديث التقدم
     */
    updateProgress(processed, total, message, stats) {
        // تحديث الأرقام
        this.elements.processedCount.textContent = processed;
        this.elements.totalCount.textContent = total;
        this.elements.currentApiKey.textContent = window.apiManager.getCurrentKeyIndex();
        
        // تحديث شريط التقدم
        const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
        this.elements.mainProgressBar.style.width = percentage + '%';
        this.elements.progressPercentage.textContent = percentage + '%';
        
        // تحديث الوقت المتبقي
        if (stats.estimatedTimeRemaining) {
            this.elements.estimatedTime.textContent = this.aiEngine.formatTimeRemaining(stats.estimatedTimeRemaining);
        }
        
        // تحديث النص الحالي
        this.elements.currentProcessingText.textContent = message || 'جاري المعالجة...';
        
        // تحديث جدول النتائج
        this.updateResultsTable();
    }
    
    /**
     * عند اكتمال المعالجة
     */
    onProcessingComplete(stats) {
        this.resetProcessingControls();
        this.showSuccess(`تم إكمال المعالجة بنجاح! تم معالجة ${stats.completed} من ${stats.total} خلية.`);
        this.updateResultsTable();
        this.elements.resultsSection.style.display = 'block';
    }
    
    /**
     * عند حدوث خطأ في المعالجة
     */
    onProcessingError(error, stats) {
        this.resetProcessingControls();
        this.showError('خطأ في المعالجة: ' + error.message);
        this.updateResultsTable();
    }
    
    /**
     * عند تغيير الحالة
     */
    onStatusChange(status, stats) {
        const statusMessages = {
            'started': 'بدأت المعالجة',
            'paused': 'تم إيقاف المعالجة مؤقتاً',
            'resumed': 'تم استئناف المعالجة',
            'stopped': 'تم إيقاف المعالجة'
        };
        
        if (statusMessages[status]) {
            this.showInfo(statusMessages[status]);
        }
    }
    
    /**
     * تحديث معاينة البيانات
     */
    updateDataPreview(preview) {
        // يمكن إضافة معاينة للبيانات هنا
        console.log('معاينة البيانات:', preview);
    }
    
    /**
     * تحديث جدول النتائج
     */
    updateResultsTable() {
        const data = this.searchQuery ? 
            this.fileProcessor.searchData(this.searchQuery) : 
            this.fileProcessor.data;
        
        this.currentData = data;
        this.renderResultsTable(data);
    }
    
    /**
     * عرض جدول النتائج
     */
    renderResultsTable(data) {
        const tbody = this.elements.resultsTableBody;
        tbody.innerHTML = '';
        
        data.forEach((item, index) => {
            const row = document.createElement('tr');
            row.className = this.getRowClass(item.status);
            
            row.innerHTML = `
                <td>${item.id}</td>
                <td>
                    <div class="original-content">
                        ${this.truncateText(item.original, 100)}
                    </div>
                </td>
                <td>
                    <div class="processed-content">
                        ${item.processed ? this.formatProcessedContent(item.processed) : 
                          `<span class="text-muted">${this.getStatusText(item.status)}</span>`}
                    </div>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-sm" onclick="uiManager.viewFullContent(${item.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${item.processed ? `
                        <button class="btn btn-outline-success btn-sm" onclick="uiManager.copyContent(${item.id})">
                            <i class="fas fa-copy"></i>
                        </button>
                        ` : ''}
                    </div>
                </td>
            `;
            
            tbody.appendChild(row);
        });
        
        // إظهار قسم النتائج إذا كان هناك بيانات
        if (data.length > 0) {
            this.elements.resultsSection.style.display = 'block';
        }
    }
    
    /**
     * الحصول على فئة الصف حسب الحالة
     */
    getRowClass(status) {
        const classes = {
            'pending': '',
            'processing': 'table-warning',
            'completed': 'table-success',
            'failed': 'table-danger'
        };
        return classes[status] || '';
    }
    
    /**
     * الحصول على نص الحالة
     */
    getStatusText(status) {
        const statusTexts = {
            'pending': 'في الانتظار',
            'processing': 'قيد المعالجة...',
            'completed': 'مكتمل',
            'failed': 'فشل'
        };
        return statusTexts[status] || status;
    }
    
    /**
     * تنسيق المحتوى المعالج
     */
    formatProcessedContent(content) {
        // تحويل النص إلى HTML مع الحفاظ على التنسيق
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }
    
    /**
     * اقتطاع النص
     */
    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
    
    /**
     * تبديل البحث
     */
    toggleSearch() {
        const isVisible = this.elements.searchBox.style.display !== 'none';
        this.elements.searchBox.style.display = isVisible ? 'none' : 'block';
        
        if (!isVisible) {
            this.elements.searchInput.focus();
        } else {
            this.elements.searchInput.value = '';
            this.handleSearch('');
        }
    }
    
    /**
     * معالجة البحث
     */
    handleSearch(query) {
        this.searchQuery = query.trim();
        this.updateResultsTable();
    }
    
    /**
     * عرض المحتوى كاملاً
     */
    viewFullContent(itemId) {
        const item = this.fileProcessor.data.find(d => d.id === itemId);
        if (item) {
            // إنشاء مودال لعرض المحتوى
            this.showContentModal(item);
        }
    }
    
    /**
     * نسخ المحتوى
     */
    async copyContent(itemId) {
        const item = this.fileProcessor.data.find(d => d.id === itemId);
        if (item && item.processed) {
            try {
                await navigator.clipboard.writeText(item.processed);
                this.showSuccess('تم نسخ المحتوى بنجاح');
            } catch (error) {
                this.showError('فشل في نسخ المحتوى');
            }
        }
    }
    
    /**
     * تصدير البيانات
     */
    exportData(format) {
        if (!this.exportManager) {
            this.showError('مدير التصدير غير متاح');
            return;
        }
        
        this.exportManager.exportData(format, this.fileProcessor.data);
    }
    
    /**
     * إظهار الإعدادات
     */
    showSettings() {
        if (this.settingsManager) {
            this.settingsManager.showSettings();
        }
    }
    
    /**
     * إظهار المساعدة
     */
    showHelp() {
        // إنشاء مودال المساعدة
        this.showHelpModal();
    }
    
    /**
     * تمكين أزرار التحكم في المعالجة
     */
    enableProcessingControls() {
        this.elements.startProcessing.disabled = false;
    }
    
    /**
     * تعطيل أزرار التحكم في المعالجة
     */
    disableProcessingControls() {
        this.elements.startProcessing.disabled = true;
    }
    
    /**
     * إعادة تعيين أزرار التحكم
     */
    resetProcessingControls() {
        this.elements.startProcessing.disabled = false;
        this.elements.pauseProcessing.disabled = true;
        this.elements.stopProcessing.disabled = true;
        this.elements.pauseProcessing.textContent = 'إيقاف مؤقت';
        this.elements.pauseProcessing.onclick = () => this.pauseProcessing();
    }
    
    /**
     * إظهار رسالة نجاح
     */
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    /**
     * إظهار رسالة خطأ
     */
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    /**
     * إظهار رسالة تحذير
     */
    showWarning(message) {
        this.showNotification(message, 'warning');
    }
    
    /**
     * إظهار رسالة معلومات
     */
    showInfo(message) {
        this.showNotification(message, 'info');
    }
    
    /**
     * إظهار إشعار
     */
    showNotification(message, type) {
        // يمكن استخدام مكتبة إشعارات أو إنشاء نظام إشعارات مخصص
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // إنشاء إشعار بسيط
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        // إزالة الإشعار بعد 5 ثوان
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);
    }
    
    /**
     * إظهار شاشة التحميل
     */
    showLoading(message = 'جاري التحميل...') {
        // يمكن إنشاء شاشة تحميل مخصصة
        console.log('Loading:', message);
    }
    
    /**
     * إخفاء شاشة التحميل
     */
    hideLoading() {
        console.log('Loading hidden');
    }
    
    /**
     * إظهار مودال المحتوى
     */
    showContentModal(item) {
        // إنشاء مودال لعرض المحتوى كاملاً
        // يمكن تطوير هذا لاحقاً
        alert(`المحتوى الأصلي:\n${item.original}\n\nالمحتوى المحسن:\n${item.processed || 'لم تتم المعالجة'}`);
    }
    
    /**
     * إظهار مودال المساعدة
     */
    showHelpModal() {
        // إنشاء مودال المساعدة
        // يمكن تطوير هذا لاحقاً
        alert('مساعدة الأداة:\n\n1. ارفع ملف Excel أو الصق النص\n2. اكتب التعليمات للذكاء الصناعي\n3. اضغط بدء المعالجة\n4. انتظر النتائج وقم بتصديرها');
    }
}

// إنشاء مثيل مدير الواجهة
const uiManager = new UIManager();

// تصدير المدير
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIManager;
} else {
    window.UIManager = UIManager;
    window.uiManager = uiManager;
}
