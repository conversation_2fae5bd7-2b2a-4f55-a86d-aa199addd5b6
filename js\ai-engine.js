/**
 * AI Engine - محرك الذكاء الصناعي
 * معالجة الطلبات وإدارة المعالجة المتقدمة
 */

class AIEngine {
    constructor() {
        this.apiManager = window.apiManager;
        this.fileProcessor = null;
        this.isProcessing = false;
        this.isPaused = false;
        this.currentBatch = 0;
        this.totalBatches = 0;
        this.processedCount = 0;
        this.totalCount = 0;
        this.startTime = null;
        this.pauseTime = null;
        this.errors = [];
        this.onProgress = null;
        this.onComplete = null;
        this.onError = null;
        this.onStatusChange = null;
    }
    
    /**
     * تهيئة المحرك
     */
    initialize(fileProcessor) {
        this.fileProcessor = fileProcessor;
        this.reset();
    }
    
    /**
     * إعادة تعيين المحرك
     */
    reset() {
        this.isProcessing = false;
        this.isPaused = false;
        this.currentBatch = 0;
        this.totalBatches = 0;
        this.processedCount = 0;
        this.totalCount = 0;
        this.startTime = null;
        this.pauseTime = null;
        this.errors = [];
    }
    
    /**
     * بدء المعالجة
     */
    async startProcessing(prompt, batchSize = 10, delay = 2) {
        if (this.isProcessing) {
            throw new Error('المعالجة قيد التشغيل بالفعل');
        }
        
        if (!this.fileProcessor || !this.fileProcessor.data.length) {
            throw new Error('لا توجد بيانات للمعالجة');
        }
        
        // تحضير المعالجة
        this.isProcessing = true;
        this.isPaused = false;
        this.startTime = Date.now();
        this.totalCount = this.fileProcessor.data.length;
        this.totalBatches = Math.ceil(this.totalCount / batchSize);
        this.processedCount = 0;
        this.currentBatch = 0;
        this.errors = [];
        
        this.notifyStatusChange('started');
        
        try {
            await this.processBatches(prompt, batchSize, delay);
            
            if (!this.isPaused) {
                this.isProcessing = false;
                this.notifyComplete();
            }
            
        } catch (error) {
            this.isProcessing = false;
            this.notifyError(error);
            throw error;
        }
    }
    
    /**
     * معالجة الدفعات
     */
    async processBatches(prompt, batchSize, delay) {
        let startIndex = 0;
        
        while (startIndex < this.totalCount && this.isProcessing && !this.isPaused) {
            this.currentBatch++;
            
            // الحصول على الدفعة الحالية
            const batchInfo = this.fileProcessor.getBatch(batchSize, startIndex);
            const batch = batchInfo.batch;
            
            this.notifyProgress(this.processedCount, this.totalCount, 
                `معالجة الدفعة ${this.currentBatch} من ${this.totalBatches}`);
            
            try {
                // معالجة الدفعة
                await this.processBatch(batch, prompt, startIndex);
                
                // تحديث العداد
                this.processedCount += batch.length;
                startIndex += batchSize;
                
                // تأخير بين الدفعات (إلا إذا كانت الدفعة الأخيرة)
                if (startIndex < this.totalCount && this.isProcessing && !this.isPaused) {
                    await this.sleep(delay * 1000);
                }
                
            } catch (error) {
                console.error(`خطأ في معالجة الدفعة ${this.currentBatch}:`, error);
                this.errors.push({
                    batch: this.currentBatch,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                
                // في حالة فشل الدفعة، نحاول المتابعة مع الدفعة التالية
                this.processedCount += batch.length;
                startIndex += batchSize;
            }
        }
    }
    
    /**
     * معالجة دفعة واحدة
     */
    async processBatch(batch, prompt, startIndex) {
        const promises = batch.map(async (item, index) => {
            const globalIndex = startIndex + index;
            
            try {
                // تحديث حالة العنصر
                this.fileProcessor.updateStatus(globalIndex, 'processing');
                
                // إنشاء البرومبت المخصص
                const customPrompt = this.createCustomPrompt(prompt, item.original);
                
                // إرسال الطلب
                const result = await this.apiManager.makeRequest(customPrompt);
                
                // تنظيف النتيجة
                const cleanedResult = this.cleanAIResponse(result);
                
                // تحديث النتيجة
                this.fileProcessor.updateProcessedResult(globalIndex, cleanedResult, 'completed');
                
                return {
                    success: true,
                    index: globalIndex,
                    result: cleanedResult
                };
                
            } catch (error) {
                console.error(`خطأ في معالجة العنصر ${globalIndex}:`, error);
                
                // تحديث حالة الخطأ
                this.fileProcessor.updateStatus(globalIndex, 'failed', error.message);
                
                return {
                    success: false,
                    index: globalIndex,
                    error: error.message
                };
            }
        });
        
        // انتظار إكمال جميع العناصر في الدفعة
        const results = await Promise.allSettled(promises);
        
        // معالجة النتائج
        results.forEach((result, index) => {
            if (result.status === 'rejected') {
                const globalIndex = startIndex + index;
                console.error(`فشل في معالجة العنصر ${globalIndex}:`, result.reason);
                this.fileProcessor.updateStatus(globalIndex, 'failed', result.reason.message);
            }
        });
    }
    
    /**
     * إنشاء برومبت مخصص
     */
    createCustomPrompt(basePrompt, originalText) {
        // التحقق من طول النص
        if (originalText.length > CONFIG.processing.maxCellLength) {
            originalText = originalText.substring(0, CONFIG.processing.maxCellLength) + '...';
        }
        
        return `${basePrompt}

النص الأصلي المطلوب تحسينه:
"${originalText}"

تأكد من أن النتيجة النهائية تحتوي على المحتوى المحسن فقط بدون أي مقدمات أو تعليقات إضافية.`;
    }
    
    /**
     * تنظيف استجابة الذكاء الصناعي
     */
    cleanAIResponse(response) {
        if (!response) return '';
        
        // إزالة المقدمات الشائعة
        const commonPrefixes = [
            'إليك النص المحسن:',
            'النص المحسن:',
            'النتيجة:',
            'المحتوى المحسن:',
            'بعد التحسين:',
            'النسخة المحسنة:'
        ];
        
        let cleaned = response.trim();
        
        // إزالة المقدمات
        commonPrefixes.forEach(prefix => {
            if (cleaned.startsWith(prefix)) {
                cleaned = cleaned.substring(prefix.length).trim();
            }
        });
        
        // إزالة علامات الاقتباس في البداية والنهاية
        if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
            cleaned = cleaned.slice(1, -1);
        }
        
        return cleaned;
    }
    
    /**
     * إيقاف المعالجة مؤقتاً
     */
    pauseProcessing() {
        if (this.isProcessing && !this.isPaused) {
            this.isPaused = true;
            this.pauseTime = Date.now();
            this.notifyStatusChange('paused');
            return true;
        }
        return false;
    }
    
    /**
     * استئناف المعالجة
     */
    resumeProcessing() {
        if (this.isProcessing && this.isPaused) {
            this.isPaused = false;
            this.pauseTime = null;
            this.notifyStatusChange('resumed');
            return true;
        }
        return false;
    }
    
    /**
     * إيقاف المعالجة نهائياً
     */
    stopProcessing() {
        if (this.isProcessing) {
            this.isProcessing = false;
            this.isPaused = false;
            this.notifyStatusChange('stopped');
            return true;
        }
        return false;
    }
    
    /**
     * الحصول على إحصائيات المعالجة
     */
    getProcessingStats() {
        const stats = this.fileProcessor ? this.fileProcessor.getProcessingStats() : {
            total: 0, pending: 0, processing: 0, completed: 0, failed: 0, progress: 0
        };
        
        // إضافة معلومات إضافية
        stats.currentBatch = this.currentBatch;
        stats.totalBatches = this.totalBatches;
        stats.isProcessing = this.isProcessing;
        stats.isPaused = this.isPaused;
        stats.errors = this.errors.length;
        
        // حساب الوقت المتبقي
        if (this.isProcessing && this.startTime && stats.completed > 0) {
            const elapsedTime = Date.now() - this.startTime;
            const avgTimePerItem = elapsedTime / stats.completed;
            const remainingItems = stats.total - stats.completed;
            stats.estimatedTimeRemaining = Math.round((remainingItems * avgTimePerItem) / 1000); // بالثواني
        } else {
            stats.estimatedTimeRemaining = null;
        }
        
        return stats;
    }
    
    /**
     * تنسيق الوقت المتبقي
     */
    formatTimeRemaining(seconds) {
        if (!seconds || seconds <= 0) return '--';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}س ${minutes}د`;
        } else if (minutes > 0) {
            return `${minutes}د ${secs}ث`;
        } else {
            return `${secs}ث`;
        }
    }
    
    /**
     * دالة النوم
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * إشعار تغيير الحالة
     */
    notifyStatusChange(status) {
        if (this.onStatusChange) {
            this.onStatusChange(status, this.getProcessingStats());
        }
    }
    
    /**
     * إشعار التقدم
     */
    notifyProgress(processed, total, message = '') {
        if (this.onProgress) {
            this.onProgress(processed, total, message, this.getProcessingStats());
        }
    }
    
    /**
     * إشعار الإكمال
     */
    notifyComplete() {
        if (this.onComplete) {
            this.onComplete(this.getProcessingStats());
        }
    }
    
    /**
     * إشعار الخطأ
     */
    notifyError(error) {
        if (this.onError) {
            this.onError(error, this.getProcessingStats());
        }
    }
    
    /**
     * تعيين معالجات الأحداث
     */
    setEventHandlers(handlers) {
        this.onProgress = handlers.onProgress || null;
        this.onComplete = handlers.onComplete || null;
        this.onError = handlers.onError || null;
        this.onStatusChange = handlers.onStatusChange || null;
    }
}

// إنشاء مثيل محرك الذكاء الصناعي
const aiEngine = new AIEngine();

// تصدير المحرك
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIEngine;
} else {
    window.AIEngine = AIEngine;
    window.aiEngine = aiEngine;
}
