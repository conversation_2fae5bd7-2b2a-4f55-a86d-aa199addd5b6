# أداة تحرير خلايا Excel بالذكاء الصناعي

## نظرة عامة

أداة احترافية لتحرير وتحسين محتوى خلايا Excel باستخدام الذكاء الصناعي. تستخدم هذه الأداة تقنيات Gemini AI المتقدمة لتحسين أوصاف المنتجات والمحتوى بطريقة تلقائية ومتقدمة.

## الميزات الرئيسية

### 🤖 الذكاء الصناعي المتقدم
- **15 مفتاح API**: نظام تدوير ذكي لمفاتيح Gemini API
- **معالجة دفعية**: معالجة متعددة الخلايا مع تتبع التقدم
- **تحسين SEO**: تحسين المحتوى لمحركات البحث
- **دعم العربية**: معالجة متقدمة للنصوص العربية

### 📊 معالجة البيانات
- **رفع ملفات Excel**: دعم .xlsx و .xls
- **لصق النص**: إدخال مباشر للمحتوى
- **معالجة كبيرة الحجم**: تعامل مع آلاف الخلايا
- **تتبع الأخطاء**: نظام شامل لمعالجة الأخطاء

### 🎨 واجهة المستخدم
- **تصميم RTL**: دعم كامل للعربية
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **Glass Morphism**: تأثيرات بصرية حديثة
- **رسوم متحركة**: تفاعل سلس ومتقدم

### 📤 التصدير المتقدم
- **Excel**: ملفات .xlsx مع تنسيق احترافي
- **HTML**: تقارير تفاعلية مع بحث
- **PDF**: مستندات جاهزة للطباعة
- **ZIP**: أرشيف شامل بجميع الصيغ

## البنية التقنية

### الملفات الأساسية

```
Edit Excel Cells/
├── index.html              # الصفحة الرئيسية
├── css/
│   ├── main.css           # الأنماط الأساسية
│   ├── components.css     # أنماط المكونات
│   └── animations.css     # الرسوم المتحركة
├── js/
│   ├── config.js          # إعدادات التطبيق
│   ├── api-manager.js     # إدارة API
│   ├── file-processor.js  # معالجة الملفات
│   ├── ai-engine.js       # محرك الذكاء الصناعي
│   ├── ui-manager.js      # إدارة الواجهة
│   ├── export-manager.js  # نظام التصدير
│   ├── settings-manager.js # إدارة الإعدادات
│   └── main.js           # التطبيق الرئيسي
└── README.md             # هذا الملف
```

### التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **UI Framework**: Bootstrap 5
- **AI API**: Google Gemini 2.0-flash
- **File Processing**: XLSX.js
- **PDF Generation**: jsPDF
- **Compression**: JSZip
- **Icons**: Font Awesome 6
- **Fonts**: Cairo, Tajawal (Google Fonts)

## التثبيت والتشغيل

### المتطلبات
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- اتصال بالإنترنت
- مفاتيح Gemini API (مضمنة في التطبيق)

### التشغيل المحلي

1. **تحميل الملفات**
   ```bash
   git clone [repository-url]
   cd "Edit Excel Cells"
   ```

2. **تشغيل الخادم المحلي**
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # أو باستخدام Node.js
   npx serve .
   
   # أو باستخدام PHP
   php -S localhost:8000
   ```

3. **فتح التطبيق**
   - افتح المتصفح واذهب إلى: `http://localhost:8000`

### التشغيل على الخادم

1. رفع جميع الملفات إلى خادم الويب
2. التأكد من دعم HTTPS (مطلوب لـ Gemini API)
3. فتح الرابط في المتصفح

## طريقة الاستخدام

### 1. إدخال البيانات
- **رفع ملف Excel**: اسحب وأفلت ملف .xlsx أو انقر لاختياره
- **لصق النص**: الصق المحتوى مباشرة (كل سطر = خلية)

### 2. تخصيص المعالجة
- **كتابة التعليمات**: اكتب التعليمات للذكاء الصناعي
- **ضبط الإعدادات**: حجم الدفعة، التأخير، إلخ
- **اختيار الخيارات**: تحسين SEO، إضافة جداول، إلخ

### 3. بدء المعالجة
- انقر "بدء المعالجة"
- تابع التقدم في الوقت الفعلي
- يمكن الإيقاف المؤقت أو الإيقاف الكامل

### 4. مراجعة النتائج
- عرض المحتوى الأصلي والمحسن جنباً إلى جنب
- البحث والتصفية في النتائج
- تحرير النتائج يدوياً إذا لزم الأمر

### 5. التصدير
- اختيار صيغة التصدير (Excel, HTML, PDF, ZIP)
- تحميل الملف المُصدر
- مشاركة النتائج

## الإعدادات المتقدمة

### إعدادات المعالجة
- **حجم الدفعة**: 5-50 خلية (افتراضي: 10)
- **التأخير**: 1-60 ثانية (افتراضي: 2)
- **المحاولات**: 1-10 محاولات (افتراضي: 3)
- **المهلة**: 10-120 ثانية (افتراضي: 30)

### إعدادات الواجهة
- **السمة**: فاتح/داكن/تلقائي
- **اللغة**: العربية/الإنجليزية
- **الرسوم المتحركة**: تفعيل/إلغاء
- **الوضع المضغوط**: توفير مساحة

### إعدادات الذكاء الصناعي
- **النموذج**: Gemini 2.0-flash / 1.5-pro
- **درجة الإبداع**: 0-1 (افتراضي: 0.7)
- **الحد الأقصى للكلمات**: 100-4000 (افتراضي: 1000)
- **التعليمات الأساسية**: تعليمات إضافية

### إعدادات التصدير
- **الصيغة الافتراضية**: Excel/HTML/PDF/نص
- **تضمين الأصلي**: نعم/لا
- **الطابع الزمني**: نعم/لا
- **مستوى الضغط**: 1-9

## استكشاف الأخطاء

### مشاكل شائعة

**1. فشل في تحميل الملف**
- تأكد من أن الملف بصيغة .xlsx أو .xls
- تحقق من حجم الملف (أقل من 10 ميجابايت)
- تأكد من عدم وجود كلمة مرور على الملف

**2. خطأ في API**
- تحقق من الاتصال بالإنترنت
- انتظر قليلاً ثم حاول مرة أخرى
- النظام يدور المفاتيح تلقائياً

**3. بطء في المعالجة**
- قلل حجم الدفعة
- زد التأخير بين الطلبات
- تأكد من استقرار الاتصال

**4. مشاكل في التصدير**
- تأكد من دعم المتصفح للتحميل
- تحقق من مساحة التخزين المتاحة
- جرب صيغة تصدير أخرى

### رسائل الخطأ

- **"فشل في قراءة الملف"**: ملف تالف أو غير مدعوم
- **"تجاوز الحد المسموح"**: ملف كبير جداً
- **"خطأ في الشبكة"**: مشكلة في الاتصال
- **"فشل في المعالجة"**: خطأ في API أو المحتوى

## الأمان والخصوصية

### حماية البيانات
- **معالجة محلية**: البيانات تُعالج في المتصفح
- **عدم التخزين**: لا يتم حفظ البيانات على الخادم
- **تشفير الاتصال**: جميع الطلبات مشفرة (HTTPS)
- **مفاتيح آمنة**: مفاتيح API محمية ومدورة

### الخصوصية
- لا يتم جمع بيانات شخصية
- لا يتم تتبع المستخدمين
- البيانات المُرسلة لـ Gemini تُحذف تلقائياً
- إعدادات محلية فقط (localStorage)

## الدعم والمساهمة

### الحصول على المساعدة
- راجع قسم استكشاف الأخطاء
- تحقق من وحدة التحكم في المتصفح (F12)
- تأكد من تحديث المتصفح

### الإبلاغ عن المشاكل
- وصف مفصل للمشكلة
- خطوات إعادة الإنتاج
- لقطة شاشة إن أمكن
- معلومات المتصفح والنظام

### المساهمة في التطوير
- الكود مفتوح المصدر
- اتبع معايير الترميز المستخدمة
- اختبر التغييرات قبل الإرسال
- وثق أي ميزات جديدة

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. يمكنك استخدامه وتعديله بحرية.

## الإصدارات

### الإصدار 1.0.0 (الحالي)
- إطلاق أولي مع جميع الميزات الأساسية
- دعم كامل للعربية وRTL
- 15 مفتاح API مع تدوير ذكي
- تصدير متعدد الصيغ
- واجهة مستخدم متقدمة

### خطط مستقبلية
- دعم المزيد من صيغ الملفات
- تحسينات في الأداء
- ميزات تعاون جماعي
- تكامل مع خدمات التخزين السحابي

---

**تم تطوير هذه الأداة باستخدام أحدث التقنيات لتوفير تجربة مستخدم متميزة ونتائج عالية الجودة.**
