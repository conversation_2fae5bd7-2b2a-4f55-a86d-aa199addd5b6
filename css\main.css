/* Main Styles - أداة تحرير خلايا Excel بالذكاء الصناعي */

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    direction: rtl;
    text-align: right;
}

/* Header Styles */
.main-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.5rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

.header-title {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
    font-size: 2rem;
}

.header-subtitle {
    color: var(--secondary-color);
    margin: 0.5rem 0 0 0;
    font-size: 1.1rem;
}

.header-actions .btn {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: var(--transition);
}

/* Main Content */
.main-content {
    padding: 0 1rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: none;
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

/* File Upload Area */
.file-upload-area {
    border: 2px dashed #cbd5e1;
    border-radius: var(--border-radius);
    padding: 3rem 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    background: rgba(248, 250, 252, 0.5);
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--success-color);
    background: rgba(5, 150, 105, 0.1);
}

/* Form Controls */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #d1d5db;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-family: 'Tajawal', sans-serif;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    font-family: 'Cairo', sans-serif;
    border: none;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, var(--primary-color) 100%);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #10b981 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #047857 0%, var(--success-color) 100%);
    transform: translateY(-1px);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #b45309 0%, var(--warning-color) 100%);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #ef4444 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #b91c1c 0%, var(--danger-color) 100%);
    transform: translateY(-1px);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #06b6d4 100%);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #0e7490 0%, var(--info-color) 100%);
    transform: translateY(-1px);
}

/* Progress Styles */
.progress {
    border-radius: var(--border-radius);
    background-color: rgba(229, 231, 235, 0.5);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color) 0%, #3b82f6 50%, var(--success-color) 100%);
    transition: width 0.6s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-stat {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 0.5rem;
}

.progress-stat h4 {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    font-size: 1.8rem;
}

.progress-stat p {
    color: var(--secondary-color);
    margin: 0.25rem 0 0 0;
    font-size: 0.9rem;
}

/* Table Styles */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    margin: 0;
}

.table th {
    background: var(--dark-color);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-color: rgba(229, 231, 235, 0.5);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(248, 250, 252, 0.5);
}

.table-hover tbody tr:hover {
    background-color: rgba(37, 99, 235, 0.05);
}

/* Search Box */
.search-box {
    position: relative;
}

.search-box input {
    padding-right: 3rem;
}

.search-box::after {
    content: '\f002';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
}

/* Footer */
.main-footer {
    background: rgba(30, 41, 59, 0.95);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
    backdrop-filter: blur(10px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-title {
        font-size: 1.5rem;
    }
    
    .header-subtitle {
        font-size: 1rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .file-upload-area {
        padding: 2rem 1rem;
    }
    
    .progress-stat {
        margin: 0.25rem;
        padding: 0.75rem;
    }
    
    .progress-stat h4 {
        font-size: 1.4rem;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--success-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-custom {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
