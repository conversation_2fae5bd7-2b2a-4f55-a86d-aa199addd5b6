/**
 * API Manager - مدير واجهة برمجة التطبيقات
 * إدارة مفاتيح Gemini API وتدويرها
 */

class APIManager {
    constructor() {
        this.apiKeys = CONFIG.api.geminiKeys;
        this.currentKeyIndex = 0;
        this.keyUsage = new Map();
        this.requestQueue = [];
        this.isProcessing = false;
        this.rateLimiter = new Map();
        
        this.initializeUsageTracking();
    }
    
    /**
     * تهيئة تتبع استخدام المفاتيح
     */
    initializeUsageTracking() {
        this.apiKeys.forEach((key, index) => {
            this.keyUsage.set(index, {
                requestsToday: 0,
                requestsThisMinute: 0,
                lastRequestTime: 0,
                errors: 0,
                isBlocked: false,
                blockUntil: 0
            });
        });
        
        // تحميل بيانات الاستخدام من التخزين المحلي
        this.loadUsageFromStorage();
        
        // إعادة تعيين العدادات كل دقيقة
        setInterval(() => this.resetMinuteCounters(), 60000);
        
        // إعادة تعيين العدادات اليومية كل يوم
        setInterval(() => this.resetDailyCounters(), 24 * 60 * 60 * 1000);
    }
    
    /**
     * تحميل بيانات الاستخدام من التخزين المحلي
     */
    loadUsageFromStorage() {
        try {
            const stored = localStorage.getItem(CONFIG.storage.keys.apiUsage);
            if (stored) {
                const data = JSON.parse(stored);
                const today = new Date().toDateString();
                
                if (data.date === today) {
                    data.usage.forEach((usage, index) => {
                        if (this.keyUsage.has(index)) {
                            this.keyUsage.set(index, { ...this.keyUsage.get(index), ...usage });
                        }
                    });
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات الاستخدام:', error);
        }
    }
    
    /**
     * حفظ بيانات الاستخدام في التخزين المحلي
     */
    saveUsageToStorage() {
        try {
            const data = {
                date: new Date().toDateString(),
                usage: Array.from(this.keyUsage.entries()).map(([index, usage]) => ({
                    index,
                    ...usage
                }))
            };
            
            localStorage.setItem(CONFIG.storage.keys.apiUsage, JSON.stringify(data));
        } catch (error) {
            console.error('خطأ في حفظ بيانات الاستخدام:', error);
        }
    }
    
    /**
     * إعادة تعيين عدادات الدقيقة
     */
    resetMinuteCounters() {
        this.keyUsage.forEach((usage, index) => {
            usage.requestsThisMinute = 0;
        });
    }
    
    /**
     * إعادة تعيين العدادات اليومية
     */
    resetDailyCounters() {
        this.keyUsage.forEach((usage, index) => {
            usage.requestsToday = 0;
            usage.errors = 0;
        });
        this.saveUsageToStorage();
    }
    
    /**
     * الحصول على المفتاح الحالي
     */
    getCurrentKey() {
        return this.apiKeys[this.currentKeyIndex];
    }
    
    /**
     * الحصول على فهرس المفتاح الحالي
     */
    getCurrentKeyIndex() {
        return this.currentKeyIndex + 1; // +1 للعرض للمستخدم
    }
    
    /**
     * التبديل للمفتاح التالي
     */
    switchToNextKey() {
        const startIndex = this.currentKeyIndex;
        
        do {
            this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
            const usage = this.keyUsage.get(this.currentKeyIndex);
            
            // التحقق من أن المفتاح غير محظور ولم يتجاوز الحدود
            if (!usage.isBlocked && 
                usage.requestsToday < CONFIG.api.maxRequestsPerDay &&
                usage.requestsThisMinute < CONFIG.api.maxRequestsPerMinute) {
                break;
            }
        } while (this.currentKeyIndex !== startIndex);
        
        // إذا عدنا للمفتاح الأصلي، فجميع المفاتيح محظورة
        if (this.currentKeyIndex === startIndex) {
            const usage = this.keyUsage.get(this.currentKeyIndex);
            if (usage.isBlocked || 
                usage.requestsToday >= CONFIG.api.maxRequestsPerDay ||
                usage.requestsThisMinute >= CONFIG.api.maxRequestsPerMinute) {
                throw new Error('جميع مفاتيح API وصلت للحد الأقصى من الاستخدام');
            }
        }
        
        console.log(`تم التبديل للمفتاح رقم ${this.getCurrentKeyIndex()}`);
        return this.getCurrentKey();
    }
    
    /**
     * تسجيل استخدام المفتاح
     */
    recordKeyUsage(keyIndex, success = true) {
        const usage = this.keyUsage.get(keyIndex);
        if (usage) {
            usage.requestsToday++;
            usage.requestsThisMinute++;
            usage.lastRequestTime = Date.now();
            
            if (!success) {
                usage.errors++;
                
                // حظر المفتاح إذا تجاوز عدد الأخطاء الحد المسموح
                if (usage.errors >= 5) {
                    usage.isBlocked = true;
                    usage.blockUntil = Date.now() + (30 * 60 * 1000); // 30 دقيقة
                    console.warn(`تم حظر المفتاح رقم ${keyIndex + 1} لمدة 30 دقيقة بسبب كثرة الأخطاء`);
                }
            }
            
            this.saveUsageToStorage();
        }
    }
    
    /**
     * إلغاء حظر المفاتيح المنتهية الصلاحية
     */
    unblockExpiredKeys() {
        const now = Date.now();
        this.keyUsage.forEach((usage, index) => {
            if (usage.isBlocked && now > usage.blockUntil) {
                usage.isBlocked = false;
                usage.errors = 0;
                console.log(`تم إلغاء حظر المفتاح رقم ${index + 1}`);
            }
        });
    }
    
    /**
     * إرسال طلب إلى Gemini API
     */
    async makeRequest(prompt, retryCount = 0) {
        this.unblockExpiredKeys();
        
        const currentKeyIndex = this.currentKeyIndex;
        const apiKey = this.getCurrentKey();
        
        try {
            const response = await fetch(`${CONFIG.api.baseUrl}?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{ text: prompt }]
                    }]
                }),
                signal: AbortSignal.timeout(CONFIG.api.timeout)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // تسجيل الاستخدام الناجح
            this.recordKeyUsage(currentKeyIndex, true);
            
            // استخراج النص من الاستجابة
            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                return data.candidates[0].content.parts[0].text;
            } else {
                throw new Error('استجابة غير صالحة من API');
            }
            
        } catch (error) {
            console.error(`خطأ في المفتاح رقم ${currentKeyIndex + 1}:`, error.message);
            
            // تسجيل الاستخدام الفاشل
            this.recordKeyUsage(currentKeyIndex, false);
            
            // إذا كان الخطأ متعلق بالحد الأقصى أو الحظر، جرب المفتاح التالي
            if (error.message.includes('quota') || 
                error.message.includes('limit') || 
                error.message.includes('429') ||
                retryCount < CONFIG.api.maxRetries) {
                
                try {
                    this.switchToNextKey();
                    
                    // انتظار قبل المحاولة مرة أخرى
                    await new Promise(resolve => setTimeout(resolve, CONFIG.api.retryDelay));
                    
                    return await this.makeRequest(prompt, retryCount + 1);
                } catch (switchError) {
                    throw new Error('فشل في التبديل للمفتاح التالي: ' + switchError.message);
                }
            }
            
            throw error;
        }
    }
    
    /**
     * معالجة دفعة من الطلبات
     */
    async processBatch(prompts, onProgress = null) {
        const results = [];
        const total = prompts.length;
        
        for (let i = 0; i < prompts.length; i++) {
            try {
                const result = await this.makeRequest(prompts[i]);
                results.push({
                    success: true,
                    data: result,
                    index: i
                });
                
                if (onProgress) {
                    onProgress(i + 1, total, null);
                }
                
                // تأخير بين الطلبات لتجنب تجاوز الحدود
                if (i < prompts.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, CONFIG.processing.defaultDelay * 1000));
                }
                
            } catch (error) {
                results.push({
                    success: false,
                    error: error.message,
                    index: i
                });
                
                if (onProgress) {
                    onProgress(i + 1, total, error.message);
                }
            }
        }
        
        return results;
    }
    
    /**
     * الحصول على إحصائيات الاستخدام
     */
    getUsageStats() {
        const stats = {
            totalKeys: this.apiKeys.length,
            currentKey: this.getCurrentKeyIndex(),
            keysStatus: []
        };
        
        this.keyUsage.forEach((usage, index) => {
            stats.keysStatus.push({
                keyNumber: index + 1,
                requestsToday: usage.requestsToday,
                requestsThisMinute: usage.requestsThisMinute,
                errors: usage.errors,
                isBlocked: usage.isBlocked,
                isActive: index === this.currentKeyIndex
            });
        });
        
        return stats;
    }
    
    /**
     * إعادة تعيين جميع المفاتيح
     */
    resetAllKeys() {
        this.keyUsage.forEach((usage, index) => {
            usage.requestsToday = 0;
            usage.requestsThisMinute = 0;
            usage.errors = 0;
            usage.isBlocked = false;
            usage.blockUntil = 0;
        });
        
        this.currentKeyIndex = 0;
        this.saveUsageToStorage();
        
        console.log('تم إعادة تعيين جميع مفاتيح API');
    }
}

// إنشاء مثيل مدير API
const apiManager = new APIManager();

// تصدير المدير
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIManager;
} else {
    window.APIManager = APIManager;
    window.apiManager = apiManager;
}
