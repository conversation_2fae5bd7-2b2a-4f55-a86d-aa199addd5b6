/**
 * Export Manager - مدير التصدير
 * تصدير البيانات بصيغ مختلفة
 */

class ExportManager {
    constructor() {
        this.supportedFormats = ['html', 'excel', 'text', 'pdf', 'zip'];
    }
    
    /**
     * تصدير البيانات
     */
    async exportData(format, data) {
        if (!data || data.length === 0) {
            throw new Error('لا توجد بيانات للتصدير');
        }
        
        try {
            switch (format) {
                case 'html':
                    return await this.exportToHTML(data);
                case 'excel':
                    return await this.exportToExcel(data);
                case 'text':
                    return await this.exportToText(data);
                case 'pdf':
                    return await this.exportToPDF(data);
                case 'zip':
                case 'all':
                    return await this.exportToZip(data);
                default:
                    throw new Error('صيغة التصدير غير مدعومة');
            }
        } catch (error) {
            console.error('خطأ في التصدير:', error);
            throw error;
        }
    }
    
    /**
     * تصدير إلى HTML
     */
    async exportToHTML(data) {
        const html = this.generateHTML(data);
        const blob = new Blob([html], { type: 'text/html;charset=utf-8' });
        this.downloadFile(blob, 'excel-editor-results.html');
        return true;
    }
    
    /**
     * إنشاء HTML
     */
    generateHTML(data) {
        const completedData = data.filter(item => item.status === 'completed');
        const timestamp = new Date().toLocaleString('ar-SA');
        
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج تحرير Excel - ${timestamp}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; direction: rtl; }
        .search-box { margin-bottom: 20px; }
        .original-content { background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 10px; }
        .processed-content { background: #e8f5e8; padding: 10px; border-radius: 5px; }
        .highlight { background-color: yellow; }
        .stats { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="stats text-center">
            <h1><i class="fas fa-robot"></i> نتائج تحرير Excel بالذكاء الصناعي</h1>
            <p>تم إنشاء هذا التقرير في: ${timestamp}</p>
            <div class="row">
                <div class="col-md-3">
                    <h4>${data.length}</h4>
                    <p>إجمالي الخلايا</p>
                </div>
                <div class="col-md-3">
                    <h4>${completedData.length}</h4>
                    <p>تم معالجتها</p>
                </div>
                <div class="col-md-3">
                    <h4>${data.filter(item => item.status === 'failed').length}</h4>
                    <p>فشلت</p>
                </div>
                <div class="col-md-3">
                    <h4>${Math.round((completedData.length / data.length) * 100)}%</h4>
                    <p>نسبة النجاح</p>
                </div>
            </div>
        </div>
        
        <div class="search-box">
            <input type="text" class="form-control" id="searchInput" placeholder="ابحث في النتائج..." onkeyup="searchResults()">
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="resultsTable">
                <thead class="table-dark">
                    <tr>
                        <th style="width: 5%">#</th>
                        <th style="width: 45%">المحتوى الأصلي</th>
                        <th style="width: 45%">المحتوى المحسن</th>
                        <th style="width: 5%">الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(item => `
                        <tr class="result-row">
                            <td>${item.id}</td>
                            <td>
                                <div class="original-content">
                                    ${this.escapeHtml(item.original)}
                                </div>
                            </td>
                            <td>
                                <div class="processed-content">
                                    ${item.processed ? this.formatContent(item.processed) : 'لم تتم المعالجة'}
                                </div>
                            </td>
                            <td>
                                <span class="badge ${this.getStatusBadgeClass(item.status)}">
                                    ${this.getStatusText(item.status)}
                                </span>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function searchResults() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toLowerCase();
            const table = document.getElementById('resultsTable');
            const rows = table.getElementsByClassName('result-row');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent || row.innerText;
                if (text.toLowerCase().indexOf(filter) > -1) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }
    </script>
</body>
</html>`;
    }
    
    /**
     * تصدير إلى Excel
     */
    async exportToExcel(data) {
        const exportData = data.map(item => ({
            'الرقم': item.id,
            'المحتوى الأصلي': item.original,
            'المحتوى المحسن': item.processed || 'لم تتم المعالجة',
            'الحالة': this.getStatusText(item.status),
            'رقم الصف الأصلي': item.rowIndex || item.id
        }));
        
        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        
        // تنسيق العمود
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        for (let C = range.s.c; C <= range.e.c; ++C) {
            const address = XLSX.utils.encode_col(C) + "1";
            if (!worksheet[address]) continue;
            worksheet[address].s = {
                font: { bold: true },
                fill: { fgColor: { rgb: "CCCCCC" } }
            };
        }
        
        // تعيين عرض الأعمدة
        worksheet['!cols'] = [
            { wch: 10 },  // الرقم
            { wch: 50 },  // المحتوى الأصلي
            { wch: 50 },  // المحتوى المحسن
            { wch: 15 },  // الحالة
            { wch: 15 }   // رقم الصف
        ];
        
        XLSX.utils.book_append_sheet(workbook, worksheet, 'النتائج المحسنة');
        
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        
        this.downloadFile(blob, 'excel-editor-results.xlsx');
        return true;
    }
    
    /**
     * تصدير إلى نص
     */
    async exportToText(data) {
        const timestamp = new Date().toLocaleString('ar-SA');
        let content = `نتائج تحرير Excel بالذكاء الصناعي\n`;
        content += `تاريخ الإنشاء: ${timestamp}\n`;
        content += `عدد الخلايا: ${data.length}\n`;
        content += `تم معالجة: ${data.filter(item => item.status === 'completed').length}\n`;
        content += `${'='.repeat(50)}\n\n`;
        
        data.forEach((item, index) => {
            content += `${index + 1}. الخلية رقم ${item.id}\n`;
            content += `المحتوى الأصلي:\n${item.original}\n\n`;
            content += `المحتوى المحسن:\n${item.processed || 'لم تتم المعالجة'}\n\n`;
            content += `الحالة: ${this.getStatusText(item.status)}\n`;
            content += `${'-'.repeat(30)}\n\n`;
        });
        
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        this.downloadFile(blob, 'excel-editor-results.txt');
        return true;
    }
    
    /**
     * تصدير إلى PDF
     */
    async exportToPDF(data) {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });
        
        // إعداد الخط العربي (يحتاج لخط يدعم العربية)
        doc.setFont('Arial', 'normal');
        doc.setFontSize(16);
        
        // العنوان
        const title = 'نتائج تحرير Excel بالذكاء الصناعي';
        doc.text(title, 105, 20, { align: 'center' });
        
        // التاريخ
        const timestamp = new Date().toLocaleString('ar-SA');
        doc.setFontSize(12);
        doc.text(`تاريخ الإنشاء: ${timestamp}`, 105, 30, { align: 'center' });
        
        // الإحصائيات
        const stats = `عدد الخلايا: ${data.length} | تم معالجة: ${data.filter(item => item.status === 'completed').length}`;
        doc.text(stats, 105, 40, { align: 'center' });
        
        let yPosition = 60;
        const pageHeight = doc.internal.pageSize.height;
        const margin = 20;
        
        data.forEach((item, index) => {
            // التحقق من الحاجة لصفحة جديدة
            if (yPosition > pageHeight - 40) {
                doc.addPage();
                yPosition = 20;
            }
            
            // رقم الخلية
            doc.setFontSize(14);
            doc.text(`${index + 1}. الخلية رقم ${item.id}`, margin, yPosition);
            yPosition += 10;
            
            // المحتوى الأصلي
            doc.setFontSize(10);
            doc.text('المحتوى الأصلي:', margin, yPosition);
            yPosition += 5;
            
            const originalLines = doc.splitTextToSize(item.original, 170);
            doc.text(originalLines, margin + 5, yPosition);
            yPosition += originalLines.length * 5 + 5;
            
            // المحتوى المحسن
            doc.text('المحتوى المحسن:', margin, yPosition);
            yPosition += 5;
            
            const processedText = item.processed || 'لم تتم المعالجة';
            const processedLines = doc.splitTextToSize(processedText, 170);
            doc.text(processedLines, margin + 5, yPosition);
            yPosition += processedLines.length * 5 + 10;
        });
        
        doc.save('excel-editor-results.pdf');
        return true;
    }
    
    /**
     * تصدير إلى ملف مضغوط
     */
    async exportToZip(data) {
        // استخدام مكتبة JSZip
        if (typeof JSZip === 'undefined') {
            throw new Error('مكتبة JSZip غير متاحة');
        }
        
        const zip = new JSZip();
        
        // إضافة ملف HTML
        const htmlContent = this.generateHTML(data);
        zip.file('results.html', htmlContent);
        
        // إضافة ملف Excel
        const excelData = this.generateExcelData(data);
        zip.file('results.xlsx', excelData);
        
        // إضافة ملف نص
        const textContent = this.generateTextContent(data);
        zip.file('results.txt', textContent);
        
        // إضافة ملف JSON
        const jsonContent = JSON.stringify(data, null, 2);
        zip.file('results.json', jsonContent);
        
        // إضافة ملف README
        const readmeContent = this.generateReadme(data);
        zip.file('README.txt', readmeContent);
        
        // إنشاء الملف المضغوط
        const zipBlob = await zip.generateAsync({ type: 'blob' });
        this.downloadFile(zipBlob, 'excel-editor-results.zip');
        
        return true;
    }
    
    /**
     * إنشاء بيانات Excel
     */
    generateExcelData(data) {
        const exportData = data.map(item => ({
            'الرقم': item.id,
            'المحتوى الأصلي': item.original,
            'المحتوى المحسن': item.processed || 'لم تتم المعالجة',
            'الحالة': this.getStatusText(item.status)
        }));
        
        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'النتائج');
        
        return XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    }
    
    /**
     * إنشاء محتوى نصي
     */
    generateTextContent(data) {
        const timestamp = new Date().toLocaleString('ar-SA');
        let content = `نتائج تحرير Excel بالذكاء الصناعي\n`;
        content += `تاريخ الإنشاء: ${timestamp}\n`;
        content += `عدد الخلايا: ${data.length}\n\n`;
        
        data.forEach((item, index) => {
            content += `${index + 1}. الخلية رقم ${item.id}\n`;
            content += `الأصلي: ${item.original}\n`;
            content += `المحسن: ${item.processed || 'لم تتم المعالجة'}\n\n`;
        });
        
        return content;
    }
    
    /**
     * إنشاء ملف README
     */
    generateReadme(data) {
        const timestamp = new Date().toLocaleString('ar-SA');
        return `نتائج تحرير Excel بالذكاء الصناعي
========================================

تاريخ الإنشاء: ${timestamp}
عدد الخلايا: ${data.length}
تم معالجة: ${data.filter(item => item.status === 'completed').length}

الملفات المضمنة:
- results.html: عرض تفاعلي للنتائج
- results.xlsx: ملف Excel للنتائج
- results.txt: ملف نصي للنتائج
- results.json: بيانات JSON الخام

استخدم أي من هذه الملفات حسب احتياجاتك.
`;
    }
    
    /**
     * تنزيل الملف
     */
    downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    /**
     * تنسيق المحتوى
     */
    formatContent(content) {
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }
    
    /**
     * تشفير HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * الحصول على فئة شارة الحالة
     */
    getStatusBadgeClass(status) {
        const classes = {
            'pending': 'bg-secondary',
            'processing': 'bg-warning',
            'completed': 'bg-success',
            'failed': 'bg-danger'
        };
        return classes[status] || 'bg-secondary';
    }
    
    /**
     * الحصول على نص الحالة
     */
    getStatusText(status) {
        const statusTexts = {
            'pending': 'في الانتظار',
            'processing': 'قيد المعالجة',
            'completed': 'مكتمل',
            'failed': 'فشل'
        };
        return statusTexts[status] || status;
    }
}

// إنشاء مثيل مدير التصدير
const exportManager = new ExportManager();

// ربط مدير التصدير بمدير الواجهة
if (window.uiManager) {
    window.uiManager.exportManager = exportManager;
}

// تصدير المدير
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExportManager;
} else {
    window.ExportManager = ExportManager;
    window.exportManager = exportManager;
}
