/**
 * Settings Manager - مدير الإعدادات
 * إدارة إعدادات التطبيق والتخصيص
 */

class SettingsManager {
    constructor() {
        this.defaultSettings = {
            // إعدادات المعالجة
            processing: {
                batchSize: 10,
                delayBetweenRequests: 2,
                maxRetries: 3,
                timeoutSeconds: 30
            },
            
            // إعدادات الواجهة
            ui: {
                theme: 'light',
                language: 'ar',
                showAnimations: true,
                autoSave: true,
                compactMode: false
            },
            
            // إعدادات التصدير
            export: {
                defaultFormat: 'excel',
                includeOriginal: true,
                includeTimestamp: true,
                compressionLevel: 6
            },
            
            // إعدادات الذكاء الصناعي
            ai: {
                model: 'gemini-2.0-flash',
                temperature: 0.7,
                maxTokens: 1000,
                systemPrompt: ''
            },
            
            // إعدادات التخزين
            storage: {
                saveHistory: true,
                maxHistoryItems: 100,
                autoCleanup: true,
                cleanupDays: 30
            }
        };
        
        this.currentSettings = this.loadSettings();
        this.settingsModal = null;
        this.createSettingsModal();
    }
    
    /**
     * تحميل الإعدادات
     */
    loadSettings() {
        try {
            const saved = localStorage.getItem('excelEditorSettings');
            if (saved) {
                const parsed = JSON.parse(saved);
                return this.mergeSettings(this.defaultSettings, parsed);
            }
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
        }
        
        return JSON.parse(JSON.stringify(this.defaultSettings));
    }
    
    /**
     * حفظ الإعدادات
     */
    saveSettings(settings = null) {
        try {
            const toSave = settings || this.currentSettings;
            localStorage.setItem('excelEditorSettings', JSON.stringify(toSave));
            this.currentSettings = toSave;
            this.applySettings();
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    }
    
    /**
     * دمج الإعدادات
     */
    mergeSettings(defaults, saved) {
        const merged = JSON.parse(JSON.stringify(defaults));
        
        for (const category in saved) {
            if (merged[category]) {
                for (const key in saved[category]) {
                    if (merged[category].hasOwnProperty(key)) {
                        merged[category][key] = saved[category][key];
                    }
                }
            }
        }
        
        return merged;
    }
    
    /**
     * الحصول على إعداد
     */
    getSetting(category, key) {
        return this.currentSettings[category]?.[key];
    }
    
    /**
     * تعيين إعداد
     */
    setSetting(category, key, value) {
        if (!this.currentSettings[category]) {
            this.currentSettings[category] = {};
        }
        this.currentSettings[category][key] = value;
        this.saveSettings();
    }
    
    /**
     * إعادة تعيين الإعدادات
     */
    resetSettings() {
        this.currentSettings = JSON.parse(JSON.stringify(this.defaultSettings));
        this.saveSettings();
        this.updateSettingsForm();
    }
    
    /**
     * تطبيق الإعدادات
     */
    applySettings() {
        // تطبيق إعدادات الواجهة
        this.applyUISettings();
        
        // تطبيق إعدادات المعالجة
        this.applyProcessingSettings();
        
        // تطبيق إعدادات الذكاء الصناعي
        this.applyAISettings();
    }
    
    /**
     * تطبيق إعدادات الواجهة
     */
    applyUISettings() {
        const ui = this.currentSettings.ui;
        
        // تطبيق السمة
        document.body.setAttribute('data-theme', ui.theme);
        
        // تطبيق الرسوم المتحركة
        if (!ui.showAnimations) {
            document.body.classList.add('no-animations');
        } else {
            document.body.classList.remove('no-animations');
        }
        
        // تطبيق الوضع المضغوط
        if (ui.compactMode) {
            document.body.classList.add('compact-mode');
        } else {
            document.body.classList.remove('compact-mode');
        }
    }
    
    /**
     * تطبيق إعدادات المعالجة
     */
    applyProcessingSettings() {
        const processing = this.currentSettings.processing;
        
        // تحديث قيم النموذج
        const batchSizeInput = document.getElementById('batchSize');
        const delayInput = document.getElementById('delayBetweenRequests');
        
        if (batchSizeInput) batchSizeInput.value = processing.batchSize;
        if (delayInput) delayInput.value = processing.delayBetweenRequests;
    }
    
    /**
     * تطبيق إعدادات الذكاء الصناعي
     */
    applyAISettings() {
        const ai = this.currentSettings.ai;
        
        // تحديث إعدادات API Manager
        if (window.apiManager) {
            window.apiManager.updateSettings({
                temperature: ai.temperature,
                maxTokens: ai.maxTokens
            });
        }
    }
    
    /**
     * إنشاء مودال الإعدادات
     */
    createSettingsModal() {
        const modalHTML = `
        <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="settingsModalLabel">
                            <i class="fas fa-cog"></i> إعدادات التطبيق
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="nav flex-column nav-pills" id="settings-tabs" role="tablist">
                                    <button class="nav-link active" id="processing-tab" data-bs-toggle="pill" data-bs-target="#processing-settings" type="button" role="tab">
                                        <i class="fas fa-cogs"></i> المعالجة
                                    </button>
                                    <button class="nav-link" id="ui-tab" data-bs-toggle="pill" data-bs-target="#ui-settings" type="button" role="tab">
                                        <i class="fas fa-palette"></i> الواجهة
                                    </button>
                                    <button class="nav-link" id="ai-tab" data-bs-toggle="pill" data-bs-target="#ai-settings" type="button" role="tab">
                                        <i class="fas fa-robot"></i> الذكاء الصناعي
                                    </button>
                                    <button class="nav-link" id="export-tab" data-bs-toggle="pill" data-bs-target="#export-settings" type="button" role="tab">
                                        <i class="fas fa-download"></i> التصدير
                                    </button>
                                    <button class="nav-link" id="storage-tab" data-bs-toggle="pill" data-bs-target="#storage-settings" type="button" role="tab">
                                        <i class="fas fa-database"></i> التخزين
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="tab-content" id="settings-content">
                                    ${this.createProcessingSettingsTab()}
                                    ${this.createUISettingsTab()}
                                    ${this.createAISettingsTab()}
                                    ${this.createExportSettingsTab()}
                                    ${this.createStorageSettingsTab()}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" onclick="settingsManager.resetSettings()">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="settingsManager.exportSettings()">
                            <i class="fas fa-file-export"></i> تصدير الإعدادات
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="settingsManager.importSettings()">
                            <i class="fas fa-file-import"></i> استيراد الإعدادات
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="settingsManager.saveSettingsFromForm()">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                    </div>
                </div>
            </div>
        </div>`;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.settingsModal = new bootstrap.Modal(document.getElementById('settingsModal'));
    }
    
    /**
     * إنشاء تبويب إعدادات المعالجة
     */
    createProcessingSettingsTab() {
        return `
        <div class="tab-pane fade show active" id="processing-settings" role="tabpanel">
            <h6><i class="fas fa-cogs"></i> إعدادات المعالجة</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-batch-size" class="form-label">حجم الدفعة</label>
                        <input type="number" class="form-control" id="settings-batch-size" min="1" max="50">
                        <div class="form-text">عدد الخلايا التي تتم معالجتها في كل دفعة</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-delay" class="form-label">التأخير بين الطلبات (ثانية)</label>
                        <input type="number" class="form-control" id="settings-delay" min="1" max="60">
                        <div class="form-text">فترة الانتظار بين كل دفعة</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-max-retries" class="form-label">عدد المحاولات</label>
                        <input type="number" class="form-control" id="settings-max-retries" min="1" max="10">
                        <div class="form-text">عدد المحاولات عند فشل الطلب</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-timeout" class="form-label">مهلة الانتظار (ثانية)</label>
                        <input type="number" class="form-control" id="settings-timeout" min="10" max="120">
                        <div class="form-text">الحد الأقصى لانتظار الاستجابة</div>
                    </div>
                </div>
            </div>
        </div>`;
    }
    
    /**
     * إنشاء تبويب إعدادات الواجهة
     */
    createUISettingsTab() {
        return `
        <div class="tab-pane fade" id="ui-settings" role="tabpanel">
            <h6><i class="fas fa-palette"></i> إعدادات الواجهة</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-theme" class="form-label">السمة</label>
                        <select class="form-select" id="settings-theme">
                            <option value="light">فاتح</option>
                            <option value="dark">داكن</option>
                            <option value="auto">تلقائي</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-language" class="form-label">اللغة</label>
                        <select class="form-select" id="settings-language">
                            <option value="ar">العربية</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="settings-animations">
                        <label class="form-check-label" for="settings-animations">
                            تفعيل الرسوم المتحركة
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="settings-auto-save">
                        <label class="form-check-label" for="settings-auto-save">
                            الحفظ التلقائي
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="settings-compact-mode">
                        <label class="form-check-label" for="settings-compact-mode">
                            الوضع المضغوط
                        </label>
                    </div>
                </div>
            </div>
        </div>`;
    }
    
    /**
     * إنشاء تبويب إعدادات الذكاء الصناعي
     */
    createAISettingsTab() {
        return `
        <div class="tab-pane fade" id="ai-settings" role="tabpanel">
            <h6><i class="fas fa-robot"></i> إعدادات الذكاء الصناعي</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-model" class="form-label">النموذج</label>
                        <select class="form-select" id="settings-model">
                            <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
                            <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-temperature" class="form-label">درجة الإبداع</label>
                        <input type="range" class="form-range" id="settings-temperature" min="0" max="1" step="0.1">
                        <div class="form-text">0 = محافظ، 1 = مبدع</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-max-tokens" class="form-label">الحد الأقصى للكلمات</label>
                        <input type="number" class="form-control" id="settings-max-tokens" min="100" max="4000">
                    </div>
                </div>
                <div class="col-12">
                    <div class="mb-3">
                        <label for="settings-system-prompt" class="form-label">التعليمات الأساسية</label>
                        <textarea class="form-control" id="settings-system-prompt" rows="3" placeholder="تعليمات إضافية للذكاء الصناعي..."></textarea>
                    </div>
                </div>
            </div>
        </div>`;
    }
    
    /**
     * إنشاء تبويب إعدادات التصدير
     */
    createExportSettingsTab() {
        return `
        <div class="tab-pane fade" id="export-settings" role="tabpanel">
            <h6><i class="fas fa-download"></i> إعدادات التصدير</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-default-format" class="form-label">الصيغة الافتراضية</label>
                        <select class="form-select" id="settings-default-format">
                            <option value="excel">Excel</option>
                            <option value="html">HTML</option>
                            <option value="text">نص</option>
                            <option value="pdf">PDF</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-compression" class="form-label">مستوى الضغط</label>
                        <input type="range" class="form-range" id="settings-compression" min="1" max="9" step="1">
                        <div class="form-text">1 = سريع، 9 = أصغر حجم</div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="settings-include-original">
                        <label class="form-check-label" for="settings-include-original">
                            تضمين المحتوى الأصلي
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="settings-include-timestamp">
                        <label class="form-check-label" for="settings-include-timestamp">
                            تضمين الطابع الزمني
                        </label>
                    </div>
                </div>
            </div>
        </div>`;
    }
    
    /**
     * إنشاء تبويب إعدادات التخزين
     */
    createStorageSettingsTab() {
        return `
        <div class="tab-pane fade" id="storage-settings" role="tabpanel">
            <h6><i class="fas fa-database"></i> إعدادات التخزين</h6>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-max-history" class="form-label">عدد العناصر في التاريخ</label>
                        <input type="number" class="form-control" id="settings-max-history" min="10" max="1000">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="settings-cleanup-days" class="form-label">أيام التنظيف التلقائي</label>
                        <input type="number" class="form-control" id="settings-cleanup-days" min="1" max="365">
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="settings-save-history">
                        <label class="form-check-label" for="settings-save-history">
                            حفظ تاريخ المعالجة
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="settings-auto-cleanup">
                        <label class="form-check-label" for="settings-auto-cleanup">
                            التنظيف التلقائي
                        </label>
                    </div>
                </div>
                <div class="col-12">
                    <button type="button" class="btn btn-outline-danger" onclick="settingsManager.clearAllData()">
                        <i class="fas fa-trash"></i> مسح جميع البيانات المحفوظة
                    </button>
                </div>
            </div>
        </div>`;
    }
    
    /**
     * إظهار الإعدادات
     */
    showSettings() {
        this.updateSettingsForm();
        this.settingsModal.show();
    }
    
    /**
     * تحديث نموذج الإعدادات
     */
    updateSettingsForm() {
        const settings = this.currentSettings;
        
        // إعدادات المعالجة
        document.getElementById('settings-batch-size').value = settings.processing.batchSize;
        document.getElementById('settings-delay').value = settings.processing.delayBetweenRequests;
        document.getElementById('settings-max-retries').value = settings.processing.maxRetries;
        document.getElementById('settings-timeout').value = settings.processing.timeoutSeconds;
        
        // إعدادات الواجهة
        document.getElementById('settings-theme').value = settings.ui.theme;
        document.getElementById('settings-language').value = settings.ui.language;
        document.getElementById('settings-animations').checked = settings.ui.showAnimations;
        document.getElementById('settings-auto-save').checked = settings.ui.autoSave;
        document.getElementById('settings-compact-mode').checked = settings.ui.compactMode;
        
        // إعدادات الذكاء الصناعي
        document.getElementById('settings-model').value = settings.ai.model;
        document.getElementById('settings-temperature').value = settings.ai.temperature;
        document.getElementById('settings-max-tokens').value = settings.ai.maxTokens;
        document.getElementById('settings-system-prompt').value = settings.ai.systemPrompt;
        
        // إعدادات التصدير
        document.getElementById('settings-default-format').value = settings.export.defaultFormat;
        document.getElementById('settings-compression').value = settings.export.compressionLevel;
        document.getElementById('settings-include-original').checked = settings.export.includeOriginal;
        document.getElementById('settings-include-timestamp').checked = settings.export.includeTimestamp;
        
        // إعدادات التخزين
        document.getElementById('settings-max-history').value = settings.storage.maxHistoryItems;
        document.getElementById('settings-cleanup-days').value = settings.storage.cleanupDays;
        document.getElementById('settings-save-history').checked = settings.storage.saveHistory;
        document.getElementById('settings-auto-cleanup').checked = settings.storage.autoCleanup;
    }
    
    /**
     * حفظ الإعدادات من النموذج
     */
    saveSettingsFromForm() {
        const newSettings = {
            processing: {
                batchSize: parseInt(document.getElementById('settings-batch-size').value),
                delayBetweenRequests: parseInt(document.getElementById('settings-delay').value),
                maxRetries: parseInt(document.getElementById('settings-max-retries').value),
                timeoutSeconds: parseInt(document.getElementById('settings-timeout').value)
            },
            ui: {
                theme: document.getElementById('settings-theme').value,
                language: document.getElementById('settings-language').value,
                showAnimations: document.getElementById('settings-animations').checked,
                autoSave: document.getElementById('settings-auto-save').checked,
                compactMode: document.getElementById('settings-compact-mode').checked
            },
            ai: {
                model: document.getElementById('settings-model').value,
                temperature: parseFloat(document.getElementById('settings-temperature').value),
                maxTokens: parseInt(document.getElementById('settings-max-tokens').value),
                systemPrompt: document.getElementById('settings-system-prompt').value
            },
            export: {
                defaultFormat: document.getElementById('settings-default-format').value,
                compressionLevel: parseInt(document.getElementById('settings-compression').value),
                includeOriginal: document.getElementById('settings-include-original').checked,
                includeTimestamp: document.getElementById('settings-include-timestamp').checked
            },
            storage: {
                maxHistoryItems: parseInt(document.getElementById('settings-max-history').value),
                cleanupDays: parseInt(document.getElementById('settings-cleanup-days').value),
                saveHistory: document.getElementById('settings-save-history').checked,
                autoCleanup: document.getElementById('settings-auto-cleanup').checked
            }
        };
        
        if (this.saveSettings(newSettings)) {
            this.settingsModal.hide();
            if (window.uiManager) {
                window.uiManager.showSuccess('تم حفظ الإعدادات بنجاح');
            }
        } else {
            if (window.uiManager) {
                window.uiManager.showError('فشل في حفظ الإعدادات');
            }
        }
    }
    
    /**
     * تصدير الإعدادات
     */
    exportSettings() {
        const settingsBlob = new Blob([JSON.stringify(this.currentSettings, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(settingsBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'excel-editor-settings.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        if (window.uiManager) {
            window.uiManager.showSuccess('تم تصدير الإعدادات بنجاح');
        }
    }
    
    /**
     * استيراد الإعدادات
     */
    importSettings() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const importedSettings = JSON.parse(e.target.result);
                        const mergedSettings = this.mergeSettings(this.defaultSettings, importedSettings);
                        
                        if (this.saveSettings(mergedSettings)) {
                            this.updateSettingsForm();
                            if (window.uiManager) {
                                window.uiManager.showSuccess('تم استيراد الإعدادات بنجاح');
                            }
                        }
                    } catch (error) {
                        if (window.uiManager) {
                            window.uiManager.showError('ملف الإعدادات غير صحيح');
                        }
                    }
                };
                reader.readAsText(file);
            }
        };
        
        input.click();
    }
    
    /**
     * مسح جميع البيانات
     */
    clearAllData() {
        if (confirm('هل أنت متأكد من مسح جميع البيانات المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.')) {
            try {
                // مسح جميع البيانات المتعلقة بالتطبيق
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key.startsWith('excelEditor')) {
                        keysToRemove.push(key);
                    }
                }
                
                keysToRemove.forEach(key => localStorage.removeItem(key));
                
                // إعادة تحميل الصفحة
                location.reload();
                
            } catch (error) {
                if (window.uiManager) {
                    window.uiManager.showError('فشل في مسح البيانات');
                }
            }
        }
    }
}

// إنشاء مثيل مدير الإعدادات
const settingsManager = new SettingsManager();

// ربط مدير الإعدادات بمدير الواجهة
if (window.uiManager) {
    window.uiManager.settingsManager = settingsManager;
}

// تصدير المدير
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SettingsManager;
} else {
    window.SettingsManager = SettingsManager;
    window.settingsManager = settingsManager;
}
