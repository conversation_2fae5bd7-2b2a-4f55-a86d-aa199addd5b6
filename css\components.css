/* Components Styles - مكونات واجهة المستخدم */

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

.loading-spinner-lg {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.processing {
    background-color: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
}

.status-badge.completed {
    background-color: rgba(16, 185, 129, 0.1);
    color: #047857;
}

.status-badge.error {
    background-color: rgba(239, 68, 68, 0.1);
    color: #b91c1c;
}

.status-badge.paused {
    background-color: rgba(245, 158, 11, 0.1);
    color: #b45309;
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(229, 231, 235, 0.5);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Tooltip Styles */
.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: var(--border-radius);
    font-family: 'Tajawal', sans-serif;
    font-size: 0.875rem;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
    color: #047857;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
    color: #b91c1c;
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
    color: #b45309;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(8, 145, 178, 0.1) 0%, rgba(6, 182, 212, 0.1) 100%);
    color: #0e7490;
    border-left: 4px solid var(--info-color);
}

/* Dropdown Styles */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 0.5rem 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    font-family: 'Tajawal', sans-serif;
}

.dropdown-item:hover {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.dropdown-item i {
    width: 20px;
    margin-left: 0.5rem;
}

/* Input Group Styles */
.input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.input-group .form-control {
    border-radius: 0;
}

.input-group .form-control:first-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.input-group .form-control:last-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.input-group-text {
    background-color: rgba(248, 250, 252, 0.8);
    border-color: #d1d5db;
    color: var(--secondary-color);
}

/* Tab Styles */
.nav-tabs {
    border-bottom: 2px solid rgba(229, 231, 235, 0.5);
}

.nav-tabs .nav-link {
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 1rem 1.5rem;
    color: var(--secondary-color);
    transition: var(--transition);
    font-weight: 500;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.05);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: white;
    border-color: var(--primary-color) var(--primary-color) white;
    border-width: 2px 2px 2px 2px;
}

/* Pagination Styles */
.pagination {
    margin: 0;
}

.page-link {
    border: none;
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
    padding: 0.75rem 1rem;
    color: var(--secondary-color);
    transition: var(--transition);
}

.page-link:hover {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Breadcrumb Styles */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item {
    font-size: 0.875rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: var(--secondary-color);
}

.breadcrumb-item.active {
    color: var(--primary-color);
    font-weight: 500;
}

/* List Group Styles */
.list-group {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.list-group-item {
    border-color: rgba(229, 231, 235, 0.5);
    padding: 1rem 1.5rem;
    transition: var(--transition);
}

.list-group-item:hover {
    background-color: rgba(248, 250, 252, 0.8);
}

.list-group-item.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Badge Styles */
.badge {
    border-radius: 50px;
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    font-size: 0.75rem;
}

/* Accordion Styles */
.accordion {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.accordion-item {
    border: none;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.accordion-button {
    background-color: rgba(248, 250, 252, 0.5);
    border: none;
    padding: 1.25rem 1.5rem;
    font-weight: 500;
    color: var(--dark-color);
}

.accordion-button:not(.collapsed) {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-body {
    padding: 1.5rem;
}

/* Switch/Toggle Styles */
.form-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    border-radius: 3rem;
    background-color: #d1d5db;
    border: none;
    transition: var(--transition);
}

.form-switch .form-check-input:checked {
    background-color: var(--primary-color);
}

.form-switch .form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* Range Slider Styles */
.form-range {
    height: 0.5rem;
    background-color: rgba(229, 231, 235, 0.5);
    border-radius: 1rem;
    outline: none;
}

.form-range::-webkit-slider-thumb {
    width: 1.5rem;
    height: 1.5rem;
    background-color: var(--primary-color);
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.form-range::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

/* Offcanvas Styles */
.offcanvas {
    border: none;
    box-shadow: -10px 0 25px -5px rgba(0, 0, 0, 0.1);
}

.offcanvas-header {
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    padding: 1.5rem;
}

.offcanvas-body {
    padding: 1.5rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(248, 250, 252, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.5);
    border-radius: 4px;
    transition: var(--transition);
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(100, 116, 139, 0.7);
}
