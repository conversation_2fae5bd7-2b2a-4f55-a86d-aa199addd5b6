/**
 * Main Application Script - ملف التطبيق الرئيسي
 * تهيئة وتشغيل التطبيق
 */

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل تطبيق تحرير Excel بالذكاء الصناعي');
    
    try {
        // التحقق من توفر جميع المكونات المطلوبة
        checkRequiredComponents();
        
        // تطبيق الإعدادات المحفوظة
        if (window.settingsManager) {
            window.settingsManager.applySettings();
        }
        
        // إظهار رسالة ترحيب
        showWelcomeMessage();
        
        // تهيئة معالجات الأحداث العامة
        initializeGlobalEventHandlers();
        
        console.log('تم تهيئة التطبيق بنجاح');
        
    } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
        showErrorMessage('فشل في تهيئة التطبيق: ' + error.message);
    }
});

/**
 * التحقق من توفر المكونات المطلوبة
 */
function checkRequiredComponents() {
    const requiredComponents = [
        'CONFIG',
        'APIManager',
        'FileProcessor', 
        'AIEngine',
        'UIManager',
        'ExportManager',
        'SettingsManager'
    ];
    
    const missingComponents = [];
    
    requiredComponents.forEach(component => {
        if (!window[component] && !window[component.toLowerCase()]) {
            missingComponents.push(component);
        }
    });
    
    if (missingComponents.length > 0) {
        throw new Error('المكونات التالية مفقودة: ' + missingComponents.join(', '));
    }
    
    // التحقق من توفر المكتبات الخارجية
    const requiredLibraries = [
        { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined' },
        { name: 'XLSX', check: () => typeof XLSX !== 'undefined' },
        { name: 'jsPDF', check: () => typeof window.jspdf !== 'undefined' }
    ];
    
    const missingLibraries = [];
    
    requiredLibraries.forEach(lib => {
        if (!lib.check()) {
            missingLibraries.push(lib.name);
        }
    });
    
    if (missingLibraries.length > 0) {
        console.warn('المكتبات التالية مفقودة:', missingLibraries.join(', '));
    }
}

/**
 * إظهار رسالة ترحيب
 */
function showWelcomeMessage() {
    setTimeout(() => {
        const welcomeAlert = document.createElement('div');
        welcomeAlert.className = 'alert alert-info alert-dismissible fade show position-fixed';
        welcomeAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        welcomeAlert.innerHTML = `
            <i class="fas fa-info-circle"></i>
            مرحباً بك في أداة تحرير Excel بالذكاء الصناعي!
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(welcomeAlert);
        
        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => {
            if (welcomeAlert.parentNode) {
                welcomeAlert.parentNode.removeChild(welcomeAlert);
            }
        }, 5000);
    }, 1000);
}

/**
 * إظهار رسالة خطأ
 */
function showErrorMessage(message) {
    const errorAlert = document.createElement('div');
    errorAlert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    errorAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    errorAlert.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(errorAlert);
}

/**
 * تهيئة معالجات الأحداث العامة
 */
function initializeGlobalEventHandlers() {
    // معالج أخطاء JavaScript العامة
    window.addEventListener('error', function(event) {
        console.error('خطأ JavaScript:', event.error);
        
        // إظهار رسالة خطأ للمستخدم في حالة الأخطاء الحرجة
        if (event.error && event.error.message) {
            showErrorMessage('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.');
        }
    });
    
    // معالج الأخطاء غير المعالجة في Promise
    window.addEventListener('unhandledrejection', function(event) {
        console.error('خطأ Promise غير معالج:', event.reason);
        event.preventDefault();
    });
    
    // معالج تغيير حالة الاتصال
    window.addEventListener('online', function() {
        if (window.uiManager) {
            window.uiManager.showSuccess('تم استعادة الاتصال بالإنترنت');
        }
    });
    
    window.addEventListener('offline', function() {
        if (window.uiManager) {
            window.uiManager.showWarning('انقطع الاتصال بالإنترنت');
        }
    });
    
    // معالج اختصارات لوحة المفاتيح
    document.addEventListener('keydown', function(event) {
        // Ctrl+S لحفظ الإعدادات
        if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
            if (window.settingsManager) {
                window.settingsManager.saveSettings();
                if (window.uiManager) {
                    window.uiManager.showSuccess('تم حفظ الإعدادات');
                }
            }
        }
        
        // Ctrl+E لتصدير النتائج
        if (event.ctrlKey && event.key === 'e') {
            event.preventDefault();
            if (window.uiManager && window.uiManager.exportManager) {
                const defaultFormat = window.settingsManager ? 
                    window.settingsManager.getSetting('export', 'defaultFormat') : 'excel';
                window.uiManager.exportData(defaultFormat);
            }
        }
        
        // F1 للمساعدة
        if (event.key === 'F1') {
            event.preventDefault();
            if (window.uiManager) {
                window.uiManager.showHelp();
            }
        }
        
        // Escape لإغلاق المودالات
        if (event.key === 'Escape') {
            const modals = document.querySelectorAll('.modal.show');
            modals.forEach(modal => {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            });
        }
    });
}

/**
 * دالة مساعدة لتحديد نوع المتصفح
 */
function getBrowserInfo() {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    let browserVersion = 'Unknown';
    
    if (userAgent.indexOf('Chrome') > -1) {
        browserName = 'Chrome';
        browserVersion = userAgent.match(/Chrome\/(\d+)/)[1];
    } else if (userAgent.indexOf('Firefox') > -1) {
        browserName = 'Firefox';
        browserVersion = userAgent.match(/Firefox\/(\d+)/)[1];
    } else if (userAgent.indexOf('Safari') > -1) {
        browserName = 'Safari';
        browserVersion = userAgent.match(/Version\/(\d+)/)[1];
    } else if (userAgent.indexOf('Edge') > -1) {
        browserName = 'Edge';
        browserVersion = userAgent.match(/Edge\/(\d+)/)[1];
    }
    
    return { name: browserName, version: browserVersion };
}

/**
 * دالة مساعدة للتحقق من دعم المتصفح للميزات المطلوبة
 */
function checkBrowserSupport() {
    const requiredFeatures = [
        { name: 'localStorage', check: () => typeof Storage !== 'undefined' },
        { name: 'fetch', check: () => typeof fetch !== 'undefined' },
        { name: 'Promise', check: () => typeof Promise !== 'undefined' },
        { name: 'FileReader', check: () => typeof FileReader !== 'undefined' },
        { name: 'Blob', check: () => typeof Blob !== 'undefined' }
    ];
    
    const unsupportedFeatures = [];
    
    requiredFeatures.forEach(feature => {
        if (!feature.check()) {
            unsupportedFeatures.push(feature.name);
        }
    });
    
    if (unsupportedFeatures.length > 0) {
        const message = `متصفحك لا يدعم الميزات التالية: ${unsupportedFeatures.join(', ')}. يرجى استخدام متصفح حديث.`;
        showErrorMessage(message);
        return false;
    }
    
    return true;
}

/**
 * دالة لتسجيل معلومات النظام
 */
function logSystemInfo() {
    const browserInfo = getBrowserInfo();
    const systemInfo = {
        browser: browserInfo,
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        screenResolution: `${screen.width}x${screen.height}`,
        windowSize: `${window.innerWidth}x${window.innerHeight}`,
        timestamp: new Date().toISOString()
    };
    
    console.log('معلومات النظام:', systemInfo);
    
    // حفظ معلومات النظام في localStorage للتشخيص
    try {
        localStorage.setItem('excelEditorSystemInfo', JSON.stringify(systemInfo));
    } catch (error) {
        console.warn('فشل في حفظ معلومات النظام:', error);
    }
}

/**
 * دالة لتنظيف البيانات القديمة
 */
function cleanupOldData() {
    try {
        const cleanupDays = window.settingsManager ? 
            window.settingsManager.getSetting('storage', 'cleanupDays') : 30;
        
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - cleanupDays);
        
        // البحث عن البيانات القديمة وحذفها
        for (let i = localStorage.length - 1; i >= 0; i--) {
            const key = localStorage.key(i);
            if (key && key.startsWith('excelEditorHistory_')) {
                try {
                    const data = JSON.parse(localStorage.getItem(key));
                    const dataDate = new Date(data.timestamp);
                    
                    if (dataDate < cutoffDate) {
                        localStorage.removeItem(key);
                        console.log('تم حذف البيانات القديمة:', key);
                    }
                } catch (error) {
                    // في حالة فشل تحليل البيانات، احذفها
                    localStorage.removeItem(key);
                }
            }
        }
    } catch (error) {
        console.warn('فشل في تنظيف البيانات القديمة:', error);
    }
}

/**
 * دالة لحفظ حالة التطبيق
 */
function saveApplicationState() {
    try {
        const state = {
            timestamp: new Date().toISOString(),
            version: CONFIG.version || '1.0.0',
            lastUsed: Date.now()
        };
        
        localStorage.setItem('excelEditorAppState', JSON.stringify(state));
    } catch (error) {
        console.warn('فشل في حفظ حالة التطبيق:', error);
    }
}

/**
 * دالة لاستعادة حالة التطبيق
 */
function restoreApplicationState() {
    try {
        const savedState = localStorage.getItem('excelEditorAppState');
        if (savedState) {
            const state = JSON.parse(savedState);
            console.log('تم استعادة حالة التطبيق:', state);
            return state;
        }
    } catch (error) {
        console.warn('فشل في استعادة حالة التطبيق:', error);
    }
    return null;
}

// تشغيل الفحوصات والتهيئة الإضافية
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من دعم المتصفح
    if (!checkBrowserSupport()) {
        return;
    }
    
    // تسجيل معلومات النظام
    logSystemInfo();
    
    // استعادة حالة التطبيق
    restoreApplicationState();
    
    // تنظيف البيانات القديمة (إذا كان مفعلاً)
    if (window.settingsManager && window.settingsManager.getSetting('storage', 'autoCleanup')) {
        cleanupOldData();
    }
    
    // حفظ حالة التطبيق
    saveApplicationState();
});

// حفظ حالة التطبيق عند إغلاق الصفحة
window.addEventListener('beforeunload', function() {
    saveApplicationState();
});

// تصدير الدوال المساعدة
window.AppUtils = {
    getBrowserInfo,
    checkBrowserSupport,
    logSystemInfo,
    cleanupOldData,
    saveApplicationState,
    restoreApplicationState,
    showErrorMessage
};
