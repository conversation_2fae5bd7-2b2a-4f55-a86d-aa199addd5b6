/* Animations - تأثيرات الحركة والانتقالات */

/* Keyframe Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

@keyframes progressBar {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(37, 99, 235, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(37, 99, 235, 0.8);
    }
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* Animation Classes */
.animate-fadeIn {
    animation: fadeIn 0.6s ease-out;
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInDown {
    animation: fadeInDown 0.6s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease-out;
}

.animate-slideInUp {
    animation: slideInUp 0.5s ease-out;
}

.animate-slideInDown {
    animation: slideInDown 0.5s ease-out;
}

.animate-zoomIn {
    animation: zoomIn 0.5s ease-out;
}

.animate-zoomOut {
    animation: zoomOut 0.5s ease-out;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

/* Hover Animations */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.4);
}

/* Loading Animations */
.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
    0%, 20% {
        color: rgba(0, 0, 0, 0);
        text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0),
                     0.5em 0 0 rgba(0, 0, 0, 0);
    }
    40% {
        color: black;
        text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0),
                     0.5em 0 0 rgba(0, 0, 0, 0);
    }
    60% {
        text-shadow: 0.25em 0 0 black,
                     0.5em 0 0 rgba(0, 0, 0, 0);
    }
    80%, 100% {
        text-shadow: 0.25em 0 0 black,
                     0.5em 0 0 black;
    }
}

/* Progress Bar Animations */
.progress-bar-animated {
    background-size: 1rem 1rem;
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position-x: 1rem;
    }
}

/* Notification Animations */
.notification-enter {
    animation: slideInRight 0.5s ease-out;
}

.notification-exit {
    animation: slideOutRight 0.5s ease-in;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Modal Animations */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: none;
}

/* Dropdown Animations */
.dropdown-menu {
    animation: fadeInDown 0.3s ease-out;
}

/* Tab Animations */
.tab-content .tab-pane {
    animation: fadeIn 0.4s ease-out;
}

/* Accordion Animations */
.accordion-collapse {
    transition: height 0.35s ease;
}

/* Button Loading State */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Stagger Animation for Lists */
.stagger-animation > * {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }
.stagger-animation > *:nth-child(7) { animation-delay: 0.7s; }
.stagger-animation > *:nth-child(8) { animation-delay: 0.8s; }
.stagger-animation > *:nth-child(9) { animation-delay: 0.9s; }
.stagger-animation > *:nth-child(10) { animation-delay: 1.0s; }

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
