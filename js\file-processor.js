/**
 * File Processor - معالج الملفات
 * قراءة ومعالجة ملفات Excel والنصوص
 */

class FileProcessor {
    constructor() {
        this.supportedFormats = ['.xlsx', '.xls'];
        this.maxFileSize = 10 * 1024 * 1024; // 10 MB
        this.data = [];
        this.originalData = [];
        this.processedData = [];
    }
    
    /**
     * التحقق من صحة الملف
     */
    validateFile(file) {
        const errors = [];
        
        // التحقق من حجم الملف
        if (file.size > this.maxFileSize) {
            errors.push(`حجم الملف كبير جداً. الحد الأقصى ${this.maxFileSize / (1024 * 1024)} ميجابايت`);
        }
        
        // التحقق من نوع الملف
        const fileName = file.name.toLowerCase();
        const isSupported = this.supportedFormats.some(format => fileName.endsWith(format));
        
        if (!isSupported) {
            errors.push(`نوع الملف غير مدعوم. الأنواع المدعومة: ${this.supportedFormats.join(', ')}`);
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
    
    /**
     * قراءة ملف Excel
     */
    async readExcelFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    
                    // الحصول على أول ورقة عمل
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    
                    // تحويل البيانات إلى JSON
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
                        header: 1,
                        defval: '',
                        blankrows: false
                    });
                    
                    // تنظيف البيانات وإزالة الصفوف الفارغة
                    const cleanedData = this.cleanExcelData(jsonData);
                    
                    resolve({
                        success: true,
                        data: cleanedData,
                        sheetName: firstSheetName,
                        totalSheets: workbook.SheetNames.length
                    });
                    
                } catch (error) {
                    reject({
                        success: false,
                        error: 'خطأ في قراءة ملف Excel: ' + error.message
                    });
                }
            };
            
            reader.onerror = () => {
                reject({
                    success: false,
                    error: 'خطأ في قراءة الملف'
                });
            };
            
            reader.readAsArrayBuffer(file);
        });
    }
    
    /**
     * تنظيف بيانات Excel
     */
    cleanExcelData(data) {
        const cleaned = [];
        
        for (let i = 0; i < data.length; i++) {
            const row = data[i];
            
            // تخطي الصفوف الفارغة تماماً
            if (!row || row.length === 0) continue;
            
            // البحث عن أول خلية غير فارغة في الصف
            let firstNonEmptyCell = '';
            for (let j = 0; j < row.length; j++) {
                if (row[j] && row[j].toString().trim() !== '') {
                    firstNonEmptyCell = row[j].toString().trim();
                    break;
                }
            }
            
            // إضافة الخلية إذا كانت تحتوي على محتوى
            if (firstNonEmptyCell) {
                cleaned.push({
                    id: cleaned.length + 1,
                    original: firstNonEmptyCell,
                    processed: '',
                    status: 'pending',
                    rowIndex: i + 1
                });
            }
        }
        
        return cleaned;
    }
    
    /**
     * معالجة النص المُلصق
     */
    processTextInput(text) {
        if (!text || text.trim() === '') {
            throw new Error('لا يوجد نص للمعالجة');
        }
        
        // تقسيم النص إلى أسطر
        const lines = text.split('\n')
            .map(line => line.trim())
            .filter(line => line !== '');
        
        if (lines.length === 0) {
            throw new Error('لا توجد أسطر صالحة للمعالجة');
        }
        
        // تحويل كل سطر إلى عنصر بيانات
        const data = lines.map((line, index) => ({
            id: index + 1,
            original: line,
            processed: '',
            status: 'pending',
            rowIndex: index + 1
        }));
        
        return {
            success: true,
            data: data
        };
    }
    
    /**
     * تحميل البيانات
     */
    loadData(data) {
        this.originalData = [...data];
        this.data = [...data];
        this.processedData = [];
        
        return {
            totalCells: this.data.length,
            preview: this.data.slice(0, 5) // معاينة أول 5 عناصر
        };
    }
    
    /**
     * الحصول على دفعة للمعالجة
     */
    getBatch(batchSize = 10, startIndex = 0) {
        const endIndex = Math.min(startIndex + batchSize, this.data.length);
        const batch = this.data.slice(startIndex, endIndex);
        
        return {
            batch: batch,
            startIndex: startIndex,
            endIndex: endIndex,
            hasMore: endIndex < this.data.length
        };
    }
    
    /**
     * تحديث نتيجة المعالجة
     */
    updateProcessedResult(index, processedText, status = 'completed') {
        if (index >= 0 && index < this.data.length) {
            this.data[index].processed = processedText;
            this.data[index].status = status;
            
            // إضافة للبيانات المعالجة
            this.processedData.push({
                ...this.data[index],
                processedAt: new Date().toISOString()
            });
            
            return true;
        }
        return false;
    }
    
    /**
     * تحديث حالة المعالجة
     */
    updateStatus(index, status, error = null) {
        if (index >= 0 && index < this.data.length) {
            this.data[index].status = status;
            if (error) {
                this.data[index].error = error;
            }
            return true;
        }
        return false;
    }
    
    /**
     * الحصول على إحصائيات المعالجة
     */
    getProcessingStats() {
        const stats = {
            total: this.data.length,
            pending: 0,
            processing: 0,
            completed: 0,
            failed: 0
        };
        
        this.data.forEach(item => {
            stats[item.status]++;
        });
        
        stats.progress = this.data.length > 0 ? 
            Math.round((stats.completed / stats.total) * 100) : 0;
        
        return stats;
    }
    
    /**
     * البحث في البيانات
     */
    searchData(query, searchIn = 'both') {
        if (!query || query.trim() === '') {
            return this.data;
        }
        
        const searchTerm = query.toLowerCase().trim();
        
        return this.data.filter(item => {
            const searchOriginal = searchIn === 'both' || searchIn === 'original';
            const searchProcessed = searchIn === 'both' || searchIn === 'processed';
            
            let matches = false;
            
            if (searchOriginal && item.original) {
                matches = matches || item.original.toLowerCase().includes(searchTerm);
            }
            
            if (searchProcessed && item.processed) {
                matches = matches || item.processed.toLowerCase().includes(searchTerm);
            }
            
            return matches;
        });
    }
    
    /**
     * تصفية البيانات حسب الحالة
     */
    filterByStatus(status) {
        if (!status || status === 'all') {
            return this.data;
        }
        
        return this.data.filter(item => item.status === status);
    }
    
    /**
     * إعادة تعيين البيانات
     */
    reset() {
        this.data = [...this.originalData];
        this.processedData = [];
        
        // إعادة تعيين حالة جميع العناصر
        this.data.forEach(item => {
            item.processed = '';
            item.status = 'pending';
            delete item.error;
            delete item.processedAt;
        });
    }
    
    /**
     * تصدير البيانات
     */
    exportData(format = 'json') {
        const exportData = this.data.map(item => ({
            'الرقم': item.id,
            'المحتوى الأصلي': item.original,
            'المحتوى المحسن': item.processed || 'لم تتم المعالجة',
            'الحالة': this.getStatusText(item.status),
            'رقم الصف': item.rowIndex
        }));
        
        switch (format) {
            case 'json':
                return JSON.stringify(exportData, null, 2);
            
            case 'csv':
                return this.convertToCSV(exportData);
            
            case 'excel':
                return this.convertToExcel(exportData);
            
            default:
                return exportData;
        }
    }
    
    /**
     * تحويل نص الحالة
     */
    getStatusText(status) {
        const statusMap = {
            'pending': 'في الانتظار',
            'processing': 'قيد المعالجة',
            'completed': 'مكتمل',
            'failed': 'فشل'
        };
        
        return statusMap[status] || status;
    }
    
    /**
     * تحويل إلى CSV
     */
    convertToCSV(data) {
        if (!data || data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => 
                    `"${(row[header] || '').toString().replace(/"/g, '""')}"`
                ).join(',')
            )
        ].join('\n');
        
        return csvContent;
    }
    
    /**
     * تحويل إلى Excel
     */
    convertToExcel(data) {
        const worksheet = XLSX.utils.json_to_sheet(data);
        const workbook = XLSX.utils.book_new();
        
        XLSX.utils.book_append_sheet(workbook, worksheet, CONFIG.export.excel.sheetName);
        
        return XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    }
    
    /**
     * الحصول على معاينة البيانات
     */
    getPreview(limit = 10) {
        return {
            data: this.data.slice(0, limit),
            total: this.data.length,
            hasMore: this.data.length > limit
        };
    }
}

// تصدير الفئة
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FileProcessor;
} else {
    window.FileProcessor = FileProcessor;
}
