<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة تحرير خلايا Excel بالذكاء الصناعي - محرر احترافي</title>
    <meta name="description" content="أداة احترافية لتحرير وتحسين محتوى خلايا Excel باستخدام الذكاء الصناعي. قم بتحسين أوصاف المنتجات والمحتوى بطريقة تلقائية ومتقدمة.">
    <meta name="keywords" content="تحرير Excel, ذكاء صناعي, تحسين المحتوى, SEO, أوصاف المنتجات">
    <meta name="author" content="AI Excel Editor Tool">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="أداة تحرير خلايا Excel بالذكاء الصناعي">
    <meta property="og:description" content="أداة احترافية لتحرير وتحسين محتوى خلايا Excel باستخدام الذكاء الصناعي">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="ar_AR">
    
    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "أداة تحرير خلايا Excel بالذكاء الصناعي",
        "description": "أداة احترافية لتحرير وتحسين محتوى خلايا Excel باستخدام الذكاء الصناعي",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "author": {
            "@type": "Organization",
            "name": "AI Tools Developer"
        }
    }
    </script>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="header-title">
                        <i class="fas fa-robot text-primary me-3"></i>
                        أداة تحرير خلايا Excel بالذكاء الصناعي
                    </h1>
                    <p class="header-subtitle">قم بتحسين وتطوير محتوى خلايا Excel باستخدام أحدث تقنيات الذكاء الصناعي</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="header-actions">
                        <button class="btn btn-outline-primary me-2" id="settingsBtn">
                            <i class="fas fa-cog"></i> الإعدادات
                        </button>
                        <button class="btn btn-primary" id="helpBtn">
                            <i class="fas fa-question-circle"></i> المساعدة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Input Section -->
            <section class="input-section mb-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-upload me-2"></i>
                            إدخال البيانات
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-method">
                                    <h5><i class="fas fa-file-excel text-success me-2"></i>رفع ملف Excel</h5>
                                    <div class="file-upload-area" id="fileUploadArea">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <p>اسحب وأفلت ملف Excel هنا أو انقر للاختيار</p>
                                        <input type="file" id="excelFile" accept=".xlsx,.xls" class="d-none">
                                        <button class="btn btn-outline-primary" onclick="document.getElementById('excelFile').click()">
                                            اختيار ملف
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="input-method">
                                    <h5><i class="fas fa-paste text-info me-2"></i>لصق المحتوى</h5>
                                    <textarea class="form-control" id="textInput" rows="8" 
                                              placeholder="الصق المحتوى هنا... كل سطر يمثل خلية منفصلة"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Prompt Section -->
            <section class="prompt-section mb-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-success text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-magic me-2"></i>
                            إعدادات المعالجة
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <label for="promptInput" class="form-label fw-bold">التعليمات للذكاء الصناعي:</label>
                                <textarea class="form-control" id="promptInput" rows="6" 
                                          placeholder="اكتب التعليمات التي تريد تطبيقها على كل خلية...">قم بتحسين الوصف للسيو وأضف جداول في حالة لم يحتوي الوصف الأصلي على جدول مع قوائم نقطية وأسئلة شائعة. اجعل المحتوى أكثر جاذبية واحترافية مع الحفاظ على المعنى الأصلي.</textarea>
                            </div>
                            <div class="col-md-4">
                                <div class="processing-options">
                                    <label class="form-label fw-bold">خيارات المعالجة:</label>
                                    <div class="mb-3">
                                        <label for="batchSize" class="form-label">حجم الدفعة:</label>
                                        <select class="form-select" id="batchSize">
                                            <option value="5">5 خلايا</option>
                                            <option value="10" selected>10 خلايا</option>
                                            <option value="20">20 خلية</option>
                                            <option value="50">50 خلية</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="delayBetweenRequests" class="form-label">التأخير بين الطلبات (ثانية):</label>
                                        <input type="number" class="form-control" id="delayBetweenRequests" value="2" min="1" max="10">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Control Section -->
            <section class="control-section mb-5">
                <div class="text-center">
                    <button class="btn btn-success btn-lg me-3" id="startProcessing">
                        <i class="fas fa-play me-2"></i>
                        بدء المعالجة
                    </button>
                    <button class="btn btn-warning btn-lg me-3" id="pauseProcessing" disabled>
                        <i class="fas fa-pause me-2"></i>
                        إيقاف مؤقت
                    </button>
                    <button class="btn btn-danger btn-lg" id="stopProcessing" disabled>
                        <i class="fas fa-stop me-2"></i>
                        إيقاف
                    </button>
                </div>
            </section>

            <!-- Progress Section -->
            <section class="progress-section mb-5" id="progressSection" style="display: none;">
                <div class="card shadow-lg">
                    <div class="card-header bg-info text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            تقدم المعالجة
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="progress-info mb-3">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="progress-stat">
                                        <h4 id="processedCount">0</h4>
                                        <p>تم معالجتها</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-stat">
                                        <h4 id="totalCount">0</h4>
                                        <p>إجمالي الخلايا</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-stat">
                                        <h4 id="currentApiKey">1</h4>
                                        <p>مفتاح API الحالي</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="progress-stat">
                                        <h4 id="estimatedTime">--</h4>
                                        <p>الوقت المتبقي</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="progress mb-3" style="height: 25px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 id="mainProgressBar" role="progressbar" style="width: 0%">
                                <span id="progressPercentage">0%</span>
                            </div>
                        </div>
                        <div class="current-processing">
                            <p class="mb-1"><strong>يتم معالجة:</strong></p>
                            <p class="text-muted" id="currentProcessingText">في انتظار البدء...</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="card shadow-lg">
                    <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-table me-2"></i>
                            النتائج
                        </h3>
                        <div class="results-actions">
                            <button class="btn btn-outline-light btn-sm me-2" id="searchToggle">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <div class="btn-group">
                                <button class="btn btn-outline-light btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-download"></i> تصدير
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" id="exportHtml"><i class="fas fa-code"></i> HTML</a></li>
                                    <li><a class="dropdown-item" href="#" id="exportExcel"><i class="fas fa-file-excel"></i> Excel</a></li>
                                    <li><a class="dropdown-item" href="#" id="exportText"><i class="fas fa-file-alt"></i> نص</a></li>
                                    <li><a class="dropdown-item" href="#" id="exportPdf"><i class="fas fa-file-pdf"></i> PDF</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" id="exportAll"><i class="fas fa-archive"></i> جميع الملفات</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="search-box mb-3" id="searchBox" style="display: none;">
                            <input type="text" class="form-control" id="searchInput" placeholder="ابحث في النتائج...">
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="resultsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 5%">#</th>
                                        <th style="width: 45%">المحتوى الأصلي</th>
                                        <th style="width: 45%">المحتوى المحسن</th>
                                        <th style="width: 5%">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="resultsTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="main-footer mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 أداة تحرير خلايا Excel بالذكاء الصناعي. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>مطور بتقنيات حديثة ومتقدمة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Modals -->
    <div id="modalsContainer"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    
    <!-- Custom Scripts -->
    <script src="js/config.js"></script>
    <script src="js/api-manager.js"></script>
    <script src="js/file-processor.js"></script>
    <script src="js/ai-engine.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/export-manager.js"></script>
    <script src="js/settings-manager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
